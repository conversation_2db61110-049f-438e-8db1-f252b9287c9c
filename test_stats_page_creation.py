#!/usr/bin/env python3
"""
Test Stats Page Creation

Quick test to verify the StatsPage can be created without errors
"""

import pygame
import sys

def test_stats_page_creation():
    """Test creating a StatsPage instance"""
    print("Testing StatsPage creation...")
    
    try:
        # Initialize pygame
        pygame.init()
        screen = pygame.display.set_mode((1024, 768))
        pygame.display.set_caption("Stats Page Test")
        
        print("✓ Pygame initialized")
        
        # Import and create StatsPage
        from stats_page import StatsPage
        print("✓ StatsPage imported successfully")
        
        # Create StatsPage instance
        print("Creating StatsPage instance...")
        stats_page = StatsPage(screen)
        print("✓ StatsPage created successfully!")
        
        # Check if performance provider is being used
        if hasattr(stats_page, 'stats_provider'):
            provider_type = type(stats_page.stats_provider).__name__
            print(f"✓ Stats provider: {provider_type}")
        
        # Clean up
        pygame.quit()
        
        print("\n" + "=" * 50)
        print("✅ STATS PAGE CREATION TEST PASSED!")
        print("=" * 50)
        print("The stats page can be created without errors.")
        print("Performance optimizations are active.")
        
        return True
        
    except Exception as e:
        print(f"✗ StatsPage creation failed: {e}")
        import traceback
        traceback.print_exc()
        
        # Clean up on error
        try:
            pygame.quit()
        except:
            pass
        
        return False

if __name__ == "__main__":
    success = test_stats_page_creation()
    sys.exit(0 if success else 1)
"""
Stats Preloader for the WOW Games application.

This module preloads statistics data in the background to improve
the performance of the stats page.
"""

import os
import time
import threading
import logging
import json
from datetime import datetime, timedelta

# Set up logging
logging.basicConfig(
    filename=os.path.join('data', 'stats_preloader.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Import stats database modules
try:
    from stats_db import get_stats_db_manager
    from db_connection_pool import get_connection_pool
    from stats_summary import get_stats_summary
    STATS_DB_AVAILABLE = True
except ImportError as e:
    STATS_DB_AVAILABLE = False
    logging.error(f"Stats database not available for preloader: {e}")

class StatsCache:
    """Cache for statistics data with time-based invalidation."""

    def __init__(self, cache_duration=300):  # 5 minutes default cache duration
        """
        Initialize the stats cache.

        Args:
            cache_duration: Cache duration in seconds
        """
        self.cache = {}
        self.cache_times = {}
        self.cache_duration = cache_duration
        self.lock = threading.RLock()

        # Create cache directory if it doesn't exist
        self.cache_dir = os.path.join('data', 'cache')
        os.makedirs(self.cache_dir, exist_ok=True)

        # Load persistent cache from disk
        self._load_persistent_cache()

        logging.info("Stats cache initialized")

    def _load_persistent_cache(self):
        """Load persistent cache from disk."""
        try:
            cache_file = os.path.join(self.cache_dir, 'stats_cache.json')
            if os.path.exists(cache_file):
                with open(cache_file, 'r') as f:
                    data = json.load(f)

                    # Only load cache entries that haven't expired
                    current_time = time.time()
                    with self.lock:
                        for key, value in data.get('cache', {}).items():
                            cache_time = data.get('cache_times', {}).get(key, 0)
                            if current_time - cache_time <= self.cache_duration:
                                self.cache[key] = value
                                self.cache_times[key] = cache_time

                logging.info(f"Loaded {len(self.cache)} items from persistent cache")
        except Exception as e:
            logging.error(f"Error loading persistent cache: {e}")

    def _save_persistent_cache(self):
        """Save cache to disk for persistence."""
        try:
            cache_file = os.path.join(self.cache_dir, 'stats_cache.json')
            with self.lock:
                data = {
                    'cache': self.cache,
                    'cache_times': self.cache_times
                }

            with open(cache_file, 'w') as f:
                json.dump(data, f)

            logging.info(f"Saved {len(self.cache)} items to persistent cache")
        except Exception as e:
            logging.error(f"Error saving persistent cache: {e}")

    def get(self, key, default=None):
        """
        Get a value from the cache.

        Args:
            key: Cache key
            default: Default value if key not found or expired

        Returns:
            The cached value or default
        """
        with self.lock:
            if key in self.cache:
                # Check if cache entry has expired
                cache_time = self.cache_times.get(key, 0)
                if time.time() - cache_time <= self.cache_duration:
                    return self.cache[key]
                else:
                    # Remove expired entry
                    del self.cache[key]
                    del self.cache_times[key]

        return default

    def set(self, key, value):
        """
        Set a value in the cache.

        Args:
            key: Cache key
            value: Value to cache
        """
        with self.lock:
            self.cache[key] = value
            self.cache_times[key] = time.time()

        # Save to disk periodically (every 10 sets)
        if len(self.cache) % 10 == 0:
            threading.Thread(target=self._save_persistent_cache).start()

    def clear(self):
        """Clear all cached data."""
        with self.lock:
            self.cache.clear()
            self.cache_times.clear()

        # Save empty cache to disk
        self._save_persistent_cache()
        logging.info("Cache cleared")

class StatsPreloader:
    """Preloader for statistics data to improve performance."""

    def __init__(self):
        """Initialize the stats preloader."""
        self.cache = StatsCache()
        self.preload_thread = None
        self.stop_event = threading.Event()

        # Initialize database manager
        if STATS_DB_AVAILABLE:
            self.stats_db = get_stats_db_manager()

            # Initialize connection pool with stats database path
            from stats_db import STATS_DB_PATH
            get_connection_pool(STATS_DB_PATH)
        else:
            self.stats_db = None

        logging.info("Stats preloader initialized")

    def start_preloading(self):
        """Start preloading statistics data in the background."""
        if self.preload_thread and self.preload_thread.is_alive():
            logging.info("Preload thread already running")
            return

        self.stop_event.clear()
        self.preload_thread = threading.Thread(target=self._preload_data)
        self.preload_thread.daemon = True
        self.preload_thread.start()

        logging.info("Started stats data preloading")

    def stop_preloading(self):
        """Stop the preloading thread."""
        if self.preload_thread and self.preload_thread.is_alive():
            self.stop_event.set()
            self.preload_thread.join(timeout=2)
            logging.info("Stopped stats data preloading")

    def _preload_data(self):
        """Preload statistics data in the background."""
        if not STATS_DB_AVAILABLE or not self.stats_db:
            logging.error("Cannot preload stats: database not available")
            return

        try:
            start_time = time.time()
            logging.info("Starting stats data preloading")

            # Preload weekly stats (optimized version)
            self._preload_weekly_stats()

            # Preload summary data
            self._preload_summary_data()

            # Preload game history
            self._preload_game_history()

            # Preload wallet data
            self._preload_wallet_data()

            elapsed_time = time.time() - start_time
            logging.info(f"Stats data preloaded successfully in {elapsed_time:.2f} seconds")

            # Save cache to disk
            self.cache._save_persistent_cache()

        except Exception as e:
            logging.error(f"Error preloading stats data: {e}")

    def _preload_weekly_stats(self):
        """Preload weekly statistics data."""
        try:
            logging.info("Loading data using optimized functions")

            # Get current date
            end_date = datetime.now()

            # Calculate start date (6 days before end date to get 7 days total)
            start_date = end_date - timedelta(days=6)

            # Generate date strings for the week
            date_list = []
            current_date = start_date
            while current_date <= end_date:
                date_list.append(current_date.strftime('%Y-%m-%d'))
                current_date += timedelta(days=1)

            # Use a single optimized query to get all weekly stats at once
            weekly_stats = self._get_weekly_stats_optimized(start_date.strftime('%Y-%m-%d'),
                                                           end_date.strftime('%Y-%m-%d'))

            # Cache the result
            self.cache.set('weekly_stats', weekly_stats)

            logging.info(f"Preloaded weekly stats for {len(weekly_stats)} days")

        except Exception as e:
            logging.error(f"Error preloading weekly stats: {e}")

    def _get_weekly_stats_optimized(self, start_date, end_date):
        """
        Get weekly statistics using a single optimized query.

        Args:
            start_date: Start date string in format 'YYYY-MM-DD'
            end_date: End date string in format 'YYYY-MM-DD'

        Returns:
            list: List of daily statistics for the week
        """
        try:
            # Get connection from pool
            from db_connection_pool import get_connection_pool
            pool = get_connection_pool()

            with pool.get_connection() as conn:
                cursor = conn.cursor()

                # Single query to get all days in the date range
                cursor.execute('''
                SELECT date, games_played, earnings, winners, total_players
                FROM daily_stats
                WHERE date BETWEEN ? AND ?
                ORDER BY date
                ''', (start_date, end_date))

                results = cursor.fetchall()

                # Convert to dictionary format
                stats_by_date = {}
                for row in results:
                    stats_by_date[row[0]] = {
                        'date': row[0],
                        'games_played': row[1],
                        'earnings': row[2],
                        'winners': row[3],
                        'total_players': row[4]
                    }

                # Generate the full week with default values for missing dates
                weekly_stats = []
                current_date = datetime.strptime(start_date, '%Y-%m-%d')
                end = datetime.strptime(end_date, '%Y-%m-%d')

                while current_date <= end:
                    date_str = current_date.strftime('%Y-%m-%d')
                    if date_str in stats_by_date:
                        weekly_stats.append(stats_by_date[date_str])
                    else:
                        # Default values for dates with no data
                        weekly_stats.append({
                            'date': date_str,
                            'games_played': 0,
                            'earnings': 0,
                            'winners': 0,
                            'total_players': 0
                        })
                    current_date += timedelta(days=1)

                return weekly_stats

        except Exception as e:
            logging.error(f"Error getting optimized weekly stats: {e}")
            # Return empty list on error
            return []

    def _preload_summary_data(self):
        """Preload summary statistics data."""
        try:
            # Get connection from pool
            from db_connection_pool import get_connection_pool
            pool = get_connection_pool()

            with pool.get_connection() as conn:
                cursor = conn.cursor()

                # Get total earnings
                cursor.execute('SELECT SUM(earnings) FROM daily_stats')
                result = cursor.fetchone()
                total_earnings = result[0] if result and result[0] is not None else 0
                self.cache.set('total_earnings', total_earnings)

                # Get today's date
                today = datetime.now().strftime('%Y-%m-%d')

                # Get daily earnings
                cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (today,))
                result = cursor.fetchone()
                daily_earnings = result[0] if result and result[0] is not None else 0
                self.cache.set('daily_earnings', daily_earnings)

                # Get daily games played
                cursor.execute('SELECT games_played FROM daily_stats WHERE date = ?', (today,))
                result = cursor.fetchone()
                daily_games = result[0] if result and result[0] is not None else 0
                self.cache.set('daily_games', daily_games)

                # Get wallet balance
                cursor.execute('''
                SELECT balance_after FROM wallet_transactions
                ORDER BY id DESC LIMIT 1
                ''')
                result = cursor.fetchone()
                wallet_balance = result[0] if result and result[0] is not None else 0
                self.cache.set('wallet_balance', wallet_balance)

            logging.info("Preloaded summary data")

        except Exception as e:
            logging.error(f"Error preloading summary data: {e}")

    def _preload_game_history(self):
        """Preload game history data."""
        try:
            # Get connection from pool
            from db_connection_pool import get_connection_pool
            pool = get_connection_pool()

            with pool.get_connection() as conn:
                cursor = conn.cursor()

                # Get total count for pagination
                cursor.execute('SELECT COUNT(*) FROM game_history')
                result = cursor.fetchone()
                total_games = result[0] if result else 0
                self.cache.set('total_games', total_games)

                # Preload first page of game history (10 records)
                # Make sure to include tips column in the query
                cursor.execute('''
                SELECT id, date_time, username, house, stake, players, total_calls,
                       commission_percent, fee, total_prize, details, status, tips
                FROM game_history
                ORDER BY date_time DESC
                LIMIT 10
                ''')

                results = cursor.fetchall()

                # Convert to list of dictionaries
                history = []
                for row in results:
                    history.append({
                        'id': row[0],
                        'date_time': row[1],
                        'username': row[2],
                        'house': row[3],
                        'stake': row[4],
                        'players': row[5],
                        'total_calls': row[6],
                        'commission_percent': row[7],
                        'fee': row[8],
                        'total_prize': row[9],
                        'details': row[10],
                        'status': row[11],
                        'tips': row[12] if len(row) > 12 and row[12] is not None else 0
                    })

                # Cache the result
                self.cache.set('game_history_page_0', history)

            logging.info(f"Preloaded game history ({len(history)} records)")

        except Exception as e:
            logging.error(f"Error preloading game history: {e}")

    def _preload_wallet_data(self):
        """Preload wallet transaction data."""
        try:
            # Get connection from pool
            from db_connection_pool import get_connection_pool
            pool = get_connection_pool()

            with pool.get_connection() as conn:
                cursor = conn.cursor()

                # Get wallet summary
                cursor.execute('''
                SELECT SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as deposits,
                       SUM(CASE WHEN amount < 0 THEN amount ELSE 0 END) as withdrawals,
                       COUNT(*) as transaction_count
                FROM wallet_transactions
                ''')

                result = cursor.fetchone()
                if result:
                    wallet_summary = {
                        'total_deposits': result[0] or 0,
                        'total_withdrawals': result[1] or 0,
                        'transaction_count': result[2] or 0
                    }
                    self.cache.set('wallet_summary', wallet_summary)

            logging.info("Preloaded wallet data")

        except Exception as e:
            logging.error(f"Error preloading wallet data: {e}")

    def get_cached_data(self, key, default=None):
        """
        Get cached data by key.

        Args:
            key: Cache key
            default: Default value if key not found

        Returns:
            The cached value or default
        """
        return self.cache.get(key, default)

    def update_cache(self, key, value):
        """
        Update a value in the cache.

        Args:
            key: Cache key
            value: Value to cache
        """
        self.cache.set(key, value)
        logging.info(f"Updated cache for key: {key}")

    def update_game_history_cache(self, page, history, total_pages):
        """
        Update game history cache for a specific page.

        Args:
            page: Page number (0-based)
            history: List of game history records
            total_pages: Total number of pages
        """
        cache_key = f'game_history_page_{page}'
        self.cache.set(cache_key, history)
        self.cache.set('total_games', total_pages * 10)  # Estimate total games
        logging.info(f"Updated game history cache for page {page}")

    def get_game_history_page(self, page, page_size=10):
        """
        Get a page of game history, using cache if available.

        Args:
            page: Page number (0-based)
            page_size: Number of records per page

        Returns:
            tuple: (history_records, total_pages)
        """
        # Check if we have this page cached
        cache_key = f'game_history_page_{page}'
        history = self.cache.get(cache_key)

        if history is None and STATS_DB_AVAILABLE:
            try:
                # Calculate offset
                offset = page * page_size

                # Get connection from pool
                from db_connection_pool import get_connection_pool
                pool = get_connection_pool()

                with pool.get_connection() as conn:
                    cursor = conn.cursor()

                    # First check if database has any records (detect post-cleanup state)
                    cursor.execute('SELECT COUNT(*) FROM game_history')
                    total_count = cursor.fetchone()[0]

                    # If database is empty (post-cleanup), return empty result
                    if total_count == 0:
                        self.cache.set('total_games', 0)
                        self.cache.set(cache_key, [])
                        return [], 1

                    # Get game history for this page
                    cursor.execute('''
                    SELECT id, date_time, username, house, stake, players, total_calls,
                           commission_percent, fee, total_prize, details, status, tips
                    FROM game_history
                    ORDER BY date_time DESC
                    LIMIT ? OFFSET ?
                    ''', (page_size, offset))

                    results = cursor.fetchall()

                    # Convert to list of dictionaries
                    history = []
                    for row in results:
                        history.append({
                            'id': row[0],
                            'date_time': row[1],
                            'username': row[2],
                            'house': row[3],
                            'stake': row[4],
                            'players': row[5],
                            'total_calls': row[6],
                            'commission_percent': row[7],
                            'fee': row[8],
                            'total_prize': row[9],
                            'details': row[10],
                            'status': row[11],
                            'tips': row[12] if len(row) > 12 and row[12] is not None else 0
                        })

                    # Cache the result and update total count
                    self.cache.set(cache_key, history)
                    self.cache.set('total_games', total_count)

            except Exception as e:
                logging.error(f"Error getting game history page {page}: {e}")
                history = []

        # Get total games count
        total_games = self.cache.get('total_games', 0)

        # Calculate total pages
        total_pages = max(1, (total_games + page_size - 1) // page_size)

        return history or [], total_pages

    def clear(self):
        """
        Clear all cache data and force a fresh reload.

        This method clears the cache and optionally starts a new preload cycle
        to ensure fresh data is loaded.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Clear the underlying cache
            self.cache.clear()
            logging.info("Cleared all preloader cache data")

            # Stop any existing preload thread
            self.stop_preloading()

            # Start a new preload thread to refresh the data
            self.start_preloading()

            return True
        except Exception as e:
            logging.error(f"Error clearing cache data: {e}")
            return False

    def clear_game_history_cache(self):
        """
        Clear only game history cache data (useful after cleanup operations).

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Clear game history related cache entries
            cache_keys_to_remove = []
            for key in self.cache.cache.keys():
                if key.startswith('game_history_page_') or key == 'total_games':
                    cache_keys_to_remove.append(key)

            for key in cache_keys_to_remove:
                self.cache.cache.pop(key, None)
                self.cache.cache_times.pop(key, None)

            logging.info(f"Cleared {len(cache_keys_to_remove)} game history cache entries")
            return True
        except Exception as e:
            logging.error(f"Error clearing game history cache: {e}")
            return False

# Singleton instance
_stats_preloader = None

def get_stats_preloader():
    """
    Get the singleton stats preloader instance.

    Returns:
        StatsPreloader: The stats preloader instance
    """
    global _stats_preloader
    if _stats_preloader is None:
        _stats_preloader = StatsPreloader()
    return _stats_preloader

# Start preloading when module is imported
if STATS_DB_AVAILABLE:
    preloader = get_stats_preloader()
    preloader.start_preloading()

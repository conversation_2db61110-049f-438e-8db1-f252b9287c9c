import pygame
import time
import traceback
from enum import Enum
from bingo_caller import BingoCaller
from bingo_logic import <PERSON>oLog<PERSON>
from pattern_bonus_system import PatternBonusSystem

class GamePauseReason(Enum):
    """Enum for reasons why a game might be paused"""
    NONE = 0
    ADMIN = 0  # Admin control (0)
    PLAYER_CLAIM = 1  # Player claiming a win (1-1200 for cartella number)

class GameState:
    """Handles game state transitions and pause/resume functionality"""

    def __init__(self, game):
        """
        Initialize the GameState handler

        Args:
            game: Reference to the main BingoGame instance
        """
        self.game = game
        self.is_paused = False
        self.pause_reason = None
        self.pause_reason_number = None
        self.player_claim_cartella = None
        self.show_pause_reason_prompt = False
        self.show_admin_control = False
        self.show_winner_validation = False
        self.show_winner_display = False
        self.show_invalid_claim_display = False
        self.invalid_claim_reason = None
        self.winner_pattern = None
        self.validation_result = None

        # Claim type tracking (for UI display and penalisation logic)
        self.claim_type = None  # Can be "valid", "late", "missed_winner", "no_pattern", "not_registered"
        self.winning_number = None  # The number that completed the winning pattern
        self.last_winning_chance_number = None  # The last number that could have given a winning chance
        self.show_missed_winner_display = False  # Flag to show the missed winner display

        # Winning pattern tracking
        self.winner_pattern = None  # Single winning pattern (for backward compatibility)
        self.winning_patterns = []  # List of all winning patterns on the card

        # Track claimed winning patterns to prevent duplicate claims
        self.claimed_patterns = {}  # Dictionary mapping cartella_number to list of claimed pattern numbers

        # Track intentionally skipped patterns (player didn't claim when they could have)
        self.skipped_patterns = {}  # Dictionary mapping cartella_number to list of skipped pattern numbers

        # Time tracking for validation
        self.last_number_time = 0
        self.next_number_called = False  # Flag to track if next number has been called
        self.was_next_number_called = False  # Store the state of next_number_called when a claim is made
        self.claim_grace_period = 30000  # Extended grace period (30 seconds) to avoid false "late claim" errors

        # CRITICAL FIX: Add flag to track if we're checking multiple claims
        self.checking_next_claim = False  # Flag to track if we're checking multiple claims
        self.late_claim = False  # Flag to track if the current claim is late

        # CRITICAL FIX: Track previous number for better late claim detection
        self.previous_number = None  # Store the previous number that was called
        self.check_with_previous_numbers = False  # Flag to indicate we should check with previous numbers
        
        # Initialize the pattern bonus system
        self.pattern_bonus_system = PatternBonusSystem()
        
        # Track pattern bonuses awarded to players
        self.pattern_bonuses = {}  # Dictionary mapping cartella_number to list of bonus info
        
        # Track current bonus for display
        self.current_bonus_info = None  # Information about the current bonus being awarded

        # Initialize bingo caller if not already present
        if not hasattr(game, 'bingo_caller'):
            self.setup_bingo_caller()

        # Initialize bingo logic if not already present
        if not hasattr(game, 'bingo_logic'):
            self.setup_bingo_logic()

    def _get_current_commission_percentage(self):
        """Get the current commission percentage from settings, not from game object."""
        try:
            from settings_manager import SettingsManager
            settings_manager = SettingsManager()
            commission = settings_manager.get_setting('game', 'commission_percentage', 20.0)
            print(f"COMMISSION: Reading current commission from settings: {commission}%")
            return commission
        except Exception as e:
            print(f"COMMISSION: Error reading from settings, using default: {e}")
            return 20.0

    def setup_bingo_caller(self):
        """Set up the bingo caller component"""
        def number_called_callback(number):
            # Performance optimization: Minimal logging

            # Ensure the number is valid
            if not (1 <= number <= 75):
                return

            # CRITICAL FIX: Store the previous number before updating
            # This helps with accurate late claim detection
            if hasattr(self.game, 'current_number') and self.game.current_number is not None:
                self.previous_number = self.game.current_number
                print(f"Stored previous number: {self.previous_number}")

            # Update the game's called numbers list with strict duplicate checking
            self.game.current_number = number

            # Check for duplicates (without detailed logging)
            if number in self.game.called_numbers:
                return

            # Add the number to the called list
            self.game.called_numbers.append(number)

            # Update the last number time for validation
            self.last_number_time = pygame.time.get_ticks()

            # Set the flag indicating a new number has been called
            # This is used for claim validation to ensure claims are made before the next number
            self.next_number_called = True
            print(f"Set next_number_called = True for number {number} - any claims now will be considered late")

            # Force a redraw of the screen to ensure the number is highlighted
            if hasattr(self.game, 'force_redraw'):
                self.game.force_redraw = True

            # Check for winners if we have bingo logic
            if hasattr(self.game, 'bingo_logic'):
                # Process the called number (winners could be handled automatically if desired)
                self.game.bingo_logic.process_called_number(number)

        # Create the bingo caller with the callback
        self.game.bingo_caller = BingoCaller(callback=number_called_callback)

        # Load the call delay from player_storage if available
        try:
            from player_storage import load_game_settings
            settings = load_game_settings()
            if 'number_call_delay' in settings:
                # Set the call delay from player_storage settings
                self.game.bingo_caller.call_delay = settings['number_call_delay']
                print(f"Loaded bingo caller delay from player_storage: {settings['number_call_delay']}s")
            else:
                # Use the game's number_call_delay if available
                if hasattr(self.game, 'number_call_delay'):
                    self.game.bingo_caller.call_delay = self.game.number_call_delay
                    print(f"Using game's number_call_delay for bingo caller: {self.game.number_call_delay}s")
        except Exception as e:
            print(f"Error loading call delay from player_storage: {e}")
            # Use the game's number_call_delay as fallback
            if hasattr(self.game, 'number_call_delay'):
                self.game.bingo_caller.call_delay = self.game.number_call_delay
                print(f"Using game's number_call_delay as fallback: {self.game.number_call_delay}s")

    def setup_bingo_logic(self):
        """Set up the bingo logic component"""
        self.game.bingo_logic = BingoLogic(self.game)

    def start_game(self):
        """Start the game"""
        # Reset game state
        self.is_paused = False
        self.pause_reason = None
        self.pause_reason_number = None
        self.player_claim_cartella = None
        self.show_pause_reason_prompt = False
        self.show_admin_control = False
        self.show_winner_validation = False
        self.show_winner_display = False
        self.show_invalid_claim_display = False
        self.show_missed_winner_display = False
        self.invalid_claim_reason = None
        self.winner_pattern = None
        self.validation_result = None

        # Reset claimed patterns and skipped patterns
        self.claimed_patterns = {}
        self.skipped_patterns = {}
        
        # Reset pattern bonuses
        self.pattern_bonuses = {}
        self.current_bonus_info = None

        # Ensure the game's called numbers list is cleared first
        # This is critical to prevent any duplicate numbers
        self.game.called_numbers = []
        self.game.current_number = None

        # Reset the bingo caller and start calling numbers
        self.game.bingo_caller.reset()

        # Double-check that the bingo caller's called_numbers list is empty
        if self.game.bingo_caller.called_numbers:
            print("Warning: Bingo caller's called_numbers list was not empty after reset. Clearing it now.")
            self.game.bingo_caller.called_numbers = []

        # Try to load the number_call_delay from player_storage first
        try:
            from player_storage import load_game_settings
            settings = load_game_settings()
            if 'number_call_delay' in settings:
                # Use the call delay from player_storage settings
                current_delay = settings['number_call_delay']
                print(f"Using number_call_delay from player_storage: {current_delay}s")
            else:
                # Fallback to the game's number_call_delay
                current_delay = getattr(self.game, 'number_call_delay', 3.0)
                print(f"Using game's number_call_delay (player_storage value not found): {current_delay}s")
        except Exception as e:
            print(f"Error loading call delay from player_storage: {e}")
            # Fallback to the game's number_call_delay
            current_delay = getattr(self.game, 'number_call_delay', 3.0)
            print(f"Using game's number_call_delay as fallback: {current_delay}s")

        # Update the bingo caller's delay and start calling numbers
        self.game.bingo_caller.call_delay = current_delay
        self.game.bingo_caller.start_calling(delay=current_delay)
        print(f"Starting game with number call delay: {current_delay}s")

        # Initialize game in the bingo logic
        self.game.bingo_logic.start_game()

        # Update main game state
        self.game.game_started = True
        self.game.create_random_board()

        # Reset time tracking
        self.last_number_time = pygame.time.get_ticks()
        self.next_number_called = False  # Reset the next_number_called flag when starting a new game
        self.was_next_number_called = False  # Reset the was_next_number_called flag when starting a new game

        # Log the initial state for debugging
        print(f"Game started. Called numbers list is empty: {len(self.game.called_numbers) == 0}")
        print(f"Bingo caller called numbers list is empty: {len(self.game.bingo_caller.called_numbers) == 0}")

    def pause_game(self):
        """Pause the game immediately for fair play"""
        if not self.game.game_started or self.is_paused:
            return

        # Pause the bingo caller immediately
        self.game.bingo_caller.pause_calling()
        self.is_paused = True
        print("Game paused immediately in game_state_handler")

        # Reset the next_number_called flag when pausing
        # This ensures we're tracking from this pause point forward
        self.next_number_called = False

        # Show pause reason prompt
        self.show_pause_reason_prompt = True

    def resume_game(self):
        """Resume the game from pause - NEVER reset the game here"""
        print("RESUME_GAME called - This should ONLY resume the game, never reset it")

        # CRITICAL FIX: Add debug logging for fullscreen mode
        is_fullscreen = False
        try:
            is_fullscreen = bool(pygame.display.get_surface().get_flags() & pygame.FULLSCREEN)
            if is_fullscreen:
                print(f"FULLSCREEN MODE: Resuming game in fullscreen mode")
                print(f"Screen dimensions: {pygame.display.get_surface().get_size()}")
        except:
            print("Could not determine fullscreen state")

        # CRITICAL FIX: Check if we're in a post-reset-cancel state
        if hasattr(self.game, 'ui_handler') and hasattr(self.game.ui_handler, 'check_post_reset_cancel_state') and self.game.ui_handler.check_post_reset_cancel_state():
            print("CRITICAL FIX: We're in a post-reset-cancel state when resume_game was called directly")
            print("CRITICAL FIX: Ignoring resume_game call to prevent accidental reset")
            print("Please wait a few seconds before trying to resume the game")
            return

        # CRITICAL FIX: Check if reset confirmation is showing and cancel it
        if hasattr(self.game, 'ui_handler') and hasattr(self.game.ui_handler, 'show_reset_confirmation') and self.game.ui_handler.show_reset_confirmation:
            print("CRITICAL FIX: Reset confirmation dialog was open when resume_game was called directly")
            print("Closing reset confirmation dialog without resetting the game")
            self.game.ui_handler.show_reset_confirmation = False
            # Continue with resume since we're explicitly calling resume_game

        # CRITICAL: Check if we're paused before resuming
        if not self.is_paused:
            print("Game is not paused, nothing to resume")
            return

        # CRITICAL FIX: Verify that the game is actually started before resuming
        # This prevents accidentally resetting the game when it's not actually running
        if not hasattr(self.game, 'game_started') or not self.game.game_started:
            print("CRITICAL ERROR: Attempted to resume a game that hasn't been started!")
            print("This could cause the game to reset instead of resume.")
            # Don't proceed with resume if the game isn't started
            # Just reset the pause state and return
            self.is_paused = False
            return

        # CRITICAL FIX: Verify that the bingo caller exists and is initialized
        if not hasattr(self.game, 'bingo_caller') or self.game.bingo_caller is None:
            print("CRITICAL ERROR: Bingo caller is not initialized!")
            print("This could cause the game to reset instead of resume.")
            # Don't proceed with resume if the bingo caller isn't initialized
            # Just reset the pause state and return
            self.is_paused = False
            return

        # CRITICAL FIX: Add additional safety check for pending actions
        # This prevents conflicts between resume and other pending actions
        if hasattr(self.game, 'pending_action') and self.game.pending_action:
            print(f"CRITICAL WARNING: Attempting to resume while pending action '{self.game.pending_action}' exists")
            print("This could cause conflicts in game state")
            # We'll continue with the resume, but log the warning

        # Reset pause-related states ONLY - do NOT reset game state
        self.is_paused = False
        self.pause_reason = None
        self.pause_reason_number = None
        self.player_claim_cartella = None
        self.show_pause_reason_prompt = False
        self.show_admin_control = False
        self.show_winner_validation = False
        self.show_winner_display = False
        self.show_invalid_claim_display = False
        self.show_missed_winner_display = False

        # CRITICAL FIX: Reset claim-related flags
        self.checking_next_claim = False
        self.late_claim = False
        self.next_number_called = False
        self.was_next_number_called = False

        # CRITICAL FIX: Reset previous_number tracking when resuming the game
        # This ensures a clean state for the next round of claims
        self.previous_number = None
        self.check_with_previous_numbers = False
        print("Reset claim-related flags, previous_number, and check_with_previous_numbers during resume_game")

        # Reset the UI handler's pause prompt flag if it exists
        if hasattr(self.game, 'ui_handler') and hasattr(self.game.ui_handler, 'pause_prompt_just_opened'):
            self.game.ui_handler.pause_prompt_just_opened = False

        # CRITICAL: Make sure we're not accidentally resetting the game
        # We should ONLY be resuming here, never resetting

        # CRITICAL FIX: Add additional safety check before resuming the bingo caller
        try:
            # Resume the bingo caller
            print("Resuming bingo caller")
            self.game.bingo_caller.resume_calling()
        except Exception as e:
            print(f"ERROR resuming bingo caller: {e}")
            print("This might indicate a deeper issue with the game state.")
            # Don't let exceptions propagate - we want to keep the game running

        # Display a message to confirm the game has been resumed
        if hasattr(self.game, 'message'):
            self.game.message = "Game resumed"
            self.game.message_type = "info"
            self.game.message_timer = 60  # 1 second at 60 FPS

        # CRITICAL FIX: Add additional logging for fullscreen mode
        if is_fullscreen:
            print("FULLSCREEN MODE: Game successfully resumed")
            # Force a redraw to ensure UI is updated correctly in fullscreen mode
            if hasattr(self.game, 'need_redraw'):
                self.game.need_redraw = True
                
    def calculate_pattern_bonus(self, cartella_number, pattern_name):
        """
        Calculate bonus points for a pattern claim based on pattern type and call count.
        
        Args:
            cartella_number: The cartella number of the player
            pattern_name: The name of the pattern claimed
            
        Returns:
            dict: Bonus information including amount, multiplier, and description
        """
        try:
            print(f"BONUS DEBUG: Starting bonus calculation for cartella {cartella_number}, pattern {pattern_name}")
            
            # Ensure pattern_bonus_system exists
            if not hasattr(self, 'pattern_bonus_system'):
                print(f"ERROR: pattern_bonus_system not initialized")
                # Create a default bonus info
                return {
                    "pattern": pattern_name,
                    "call_count": 0,
                    "bonus_amount": 100,
                    "multiplier": 1.0,
                    "description": f"Default bonus for {pattern_name}",
                    "tier": "Standard",
                    "is_valid_claim": True
                }
            
            # Get the number of calls made when the pattern was claimed
            try:
                call_count = len(self.game.called_numbers) if hasattr(self.game, 'called_numbers') and self.game.called_numbers else 0
                print(f"BONUS DEBUG: Call count: {call_count}")
            except Exception as call_e:
                print(f"ERROR getting call count: {call_e}")
                call_count = 0
            
            # Calculate the bonus using the pattern bonus system
            try:
                print(f"BONUS DEBUG: Calling pattern_bonus_system.get_bonus_info")
                bonus_info = self.pattern_bonus_system.get_bonus_info(pattern_name, call_count)
                print(f"BONUS DEBUG: Got bonus info: {bonus_info}")
            except Exception as bonus_e:
                print(f"ERROR calculating bonus with pattern bonus system: {bonus_e}")
                traceback.print_exc()
                # Create a fallback bonus info
                bonus_info = {
                    "pattern": pattern_name,
                    "call_count": call_count,
                    "bonus_amount": 100,
                    "multiplier": 1.0,
                    "description": f"Fallback bonus for {pattern_name}",
                    "tier": "Standard",
                    "is_valid_claim": True
                }
            
            # Ensure pattern_bonuses dict exists
            try:
                if not hasattr(self, 'pattern_bonuses'):
                    print(f"BONUS DEBUG: Creating pattern_bonuses dict")
                    self.pattern_bonuses = {}
                
                # Store the bonus information for this player
                if cartella_number not in self.pattern_bonuses:
                    self.pattern_bonuses[cartella_number] = []
                
                # Add this bonus to the player's bonus list
                self.pattern_bonuses[cartella_number].append(bonus_info)
                print(f"BONUS DEBUG: Added bonus to player {cartella_number}")
                
            except Exception as storage_e:
                print(f"ERROR storing bonus information: {storage_e}")
                traceback.print_exc()
            
            # Store the current bonus info for display
            try:
                self.current_bonus_info = bonus_info
                print(f"BONUS DEBUG: Set current_bonus_info for display")
            except Exception as display_e:
                print(f"ERROR setting current bonus info: {display_e}")
            
            # Log the bonus information
            try:
                description = bonus_info.get('description', f'Bonus for {pattern_name}')
                print(f"Pattern Bonus: {description}")
            except Exception as log_e:
                print(f"ERROR logging bonus information: {log_e}")
            
            print(f"BONUS DEBUG: Bonus calculation completed successfully")
            return bonus_info
            
        except Exception as e:
            print(f"CRITICAL ERROR in calculate_pattern_bonus: {e}")
            traceback.print_exc()
            
            # Return a safe fallback bonus
            return {
                "pattern": pattern_name,
                "call_count": 0,
                "bonus_amount": 100,
                "multiplier": 1.0,
                "description": f"Emergency fallback bonus for {pattern_name}",
                "tier": "Standard",
                "is_valid_claim": True
            }

    def reset_game(self):
        """Reset the game while preserving UI state for 'remember for next session' functionality"""
        # Don't automatically return to board selection - maintain current page context
        should_return_to_board_selection = False
        print(f"Reset game called with pause_reason_number = {self.pause_reason_number}")
        print(f"Should return to board selection: {should_return_to_board_selection} (maintaining current page context)")

        # CRITICAL FIX: Add debug logging for fullscreen mode and ensure screen mode consistency
        is_fullscreen = False
        try:
            is_fullscreen = bool(pygame.display.get_surface().get_flags() & pygame.FULLSCREEN)
            if is_fullscreen:
                print(f"FULLSCREEN MODE: Resetting game in fullscreen mode")
                print(f"Screen dimensions: {pygame.display.get_surface().get_size()}")

            # Ensure screen mode consistency during reset
            try:
                from screen_mode_manager import get_screen_mode_manager
                screen_mode_manager = get_screen_mode_manager()
                current_screen = pygame.display.get_surface()
                if current_screen:
                    screen_mode_manager.ensure_consistent_mode(current_screen)
                    print("Screen mode consistency maintained during reset")
            except Exception as e:
                print(f"Error maintaining screen mode consistency during reset: {e}")
        except:
            print("Could not determine fullscreen state")

        # CRITICAL FIX: Ensure the game is in a state that can be reset
        # This is important for the reset confirmation button to work properly
        print("CRITICAL FIX: Ensuring game is in a proper state for reset")

        # CRITICAL FIX: Force the game_started flag to true to ensure reset_game() works properly
        if hasattr(self.game, 'game_started'):
            was_game_started = self.game.game_started
            print(f"CRITICAL FIX: Current game_started state: {was_game_started}")
            # Always set game_started to True before resetting to ensure proper reset
            self.game.game_started = True
            print("CRITICAL FIX: Setting game_started to True to ensure proper reset")

        # Check if we're in demo mode (properly check both the game and usage_tracker)
        is_demo_mode = False

        # Check game object first
        if hasattr(self.game, 'is_demo_mode'):
            is_demo_mode = self.game.is_demo_mode
            print(f"Demo mode from game object: {is_demo_mode}")

        # Check usage_tracker as well (it might have the more accurate value)
        if not is_demo_mode and hasattr(self.game, 'usage_tracker') and hasattr(self.game.usage_tracker, 'in_demo_mode'):
            is_demo_mode = self.game.usage_tracker.in_demo_mode
            print(f"Demo mode from usage_tracker: {is_demo_mode}")

        # Force demo mode to False if we have more than 2 players (real games)
        if hasattr(self.game, 'players') and len(self.game.players) > 2:
            is_demo_mode = False
            print(f"Forcing demo mode to False because we have {len(self.game.players)} players (> 2)")

            # Update the flags in both game and usage_tracker to ensure consistency
            self.game.is_demo_mode = False
            if hasattr(self.game, 'usage_tracker'):
                self.game.usage_tracker.in_demo_mode = False

        # ENHANCED: Record game completion for ALL scenarios, not just valid claims
        should_record_game = False
        game_completion_type = "unknown"
        winner_name = "Unknown"

        # Check if we have a valid claim and stats tracking is enabled
        if self.show_winner_display and self.validation_result and self.player_claim_cartella:
            should_record_game = True
            game_completion_type = "valid_winner"
            winner_name = f"Cartella #{self.player_claim_cartella}"

        # ENHANCED: Also record games that had activity but no valid winner
        elif (hasattr(self.game, 'called_numbers') and len(self.game.called_numbers) > 0 and
              hasattr(self.game, 'players') and len(self.game.players) > 0 and
              not is_demo_mode):
            should_record_game = True
            game_completion_type = "no_winner"
            winner_name = "No Winner"

        if should_record_game:
            try:
                from stats_page import StatsPage

                # Calculate game duration
                game_duration = 0
                if hasattr(self.game, 'bingo_caller') and hasattr(self.game.bingo_caller, 'start_time'):
                    game_duration = time.time() - self.game.bingo_caller.start_time

                # Get actual stake amount from game settings - FIXED to use correct source
                stake_amount = 25  # Updated default to match current settings

                # Try multiple sources for the bet amount
                if hasattr(self.game, 'bet_amount') and self.game.bet_amount:
                    stake_amount = self.game.bet_amount
                    print(f"DEBUG: Using game.bet_amount: {stake_amount}")
                elif hasattr(self.game, 'stake_amount') and self.game.stake_amount:
                    stake_amount = self.game.stake_amount
                    print(f"DEBUG: Using game.stake_amount: {stake_amount}")
                else:
                    # Load from game settings file as fallback
                    try:
                        from player_storage import load_game_settings
                        settings = load_game_settings()
                        stake_amount = settings.get('bet_amount', 25)
                        print(f"DEBUG: Using settings bet_amount: {stake_amount}")
                    except Exception as e:
                        print(f"DEBUG: Could not load settings, using default: {e}")
                        stake_amount = 25

                # Create game data for statistics
                game_data = {
                    "winner_name": winner_name,
                    "winner_cartella": self.player_claim_cartella if hasattr(self, 'player_claim_cartella') else 0,
                    "claim_type": self.winner_pattern or game_completion_type,
                    "game_duration": game_duration,
                    "player_count": len(self.game.players),
                    "prize_amount": getattr(self.game, 'prize_pool', 0),
                    # FIXED: Always read commission percentage from current settings
                    "commission_percentage": self._get_current_commission_percentage(),
                    "called_numbers": self.game.called_numbers,
                    "is_demo_mode": is_demo_mode,  # Pass the demo mode flag
                    "date_time": time.strftime('%Y-%m-%d %H:%M:%S'),  # Add explicit date_time
                    "stake": stake_amount,  # Add actual stake amount
                    "bet_amount": stake_amount,  # For compatibility
                    "completion_type": game_completion_type  # Track how the game ended
                }

                print("=" * 80)
                print(f"RECORDING GAME COMPLETION EVENT TO STATS - Type: {game_completion_type}")
                print(f"Game data: {game_data}")
                print(f"Demo mode: {is_demo_mode}")
                print("=" * 80)

                # First try to use the event hooks system for real-time updates
                try:
                    import importlib
                    stats_hooks_spec = importlib.util.find_spec('stats_event_hooks')
                    if stats_hooks_spec is not None:
                        print("stats_event_hooks module is available")
                        # Import the stats_event_hooks module
                        from stats_event_hooks import get_stats_event_hooks
                        stats_hooks = get_stats_event_hooks()

                        # Debug log the stats_hooks object
                        print(f"stats_hooks object: {stats_hooks}, type: {type(stats_hooks)}")

                        # Debug log method presence
                        print(f"Has on_game_completed method: {hasattr(stats_hooks, 'on_game_completed')}")

                        # Record the game completion event
                        result = stats_hooks.on_game_completed(game_data)
                        print(f"Added game completion event to stats via event hooks: {result}")

                        # Check if any events are in the queue
                        if hasattr(stats_hooks, 'queue_lock') and hasattr(stats_hooks, 'event_queue'):
                            with stats_hooks.queue_lock:
                                print(f"Events in queue: {len(stats_hooks.event_queue)}")
                                print(f"Queue contents: {stats_hooks.event_queue}")

                        # Check if worker thread is running
                        if hasattr(stats_hooks, 'worker_thread'):
                            print(f"Worker thread is alive: {stats_hooks.worker_thread.is_alive()}")
                    else:
                        print("stats_event_hooks module could not be imported, falling back to StatsPage")
                        raise ImportError("stats_event_hooks module not available")
                except Exception as hooks_e:
                    print(f"Error using stats_event_hooks: {hooks_e}")
                    import traceback
                    traceback.print_exc()

                    # Fallback to StatsPage
                    print("Falling back to StatsPage.update_game_statistics")
                    from stats_page import StatsPage
                    StatsPage.update_game_statistics(game_data)
                    print(f"Game statistics updated via StatsPage (demo mode: {is_demo_mode})")

                # Try to directly access thread_safe_db to verify database connectivity
                try:
                    print("-" * 50)
                    print("DIRECTLY TESTING DATABASE CONNECTION AFTER GAME COMPLETION")
                    print("-" * 50)
                    import thread_safe_db

                    # Test connection to database
                    conn = thread_safe_db.get_connection()
                    cursor = conn.cursor()

                    # Check if the record was actually saved
                    cursor.execute('SELECT COUNT(*) FROM game_history')
                    count = cursor.fetchone()[0]
                    print(f"Current game_history record count: {count}")

                    # Get the most recent game record
                    cursor.execute('SELECT id, date_time, username, claim_type FROM game_history ORDER BY id DESC LIMIT 1')
                    latest = cursor.fetchone()
                    if latest:
                        print(f"Latest game record: ID={latest[0]}, Date={latest[1]}, Winner={latest[2]}, Claim={latest[3]}")
                    else:
                        print("No game records found in database")

                    # Check daily_stats table
                    today = datetime.now().strftime("%Y-%m-%d")
                    cursor.execute('SELECT * FROM daily_stats WHERE date = ?', (today,))
                    result = cursor.fetchone()
                    if result:
                        print(f"Found daily stats for today: {result}")
                    else:
                        print("No daily stats found for today")

                    # Force a refresh of the database to ensure data is saved
                    print("Forcing database refresh")

                    # Double-check that we're not in demo mode before recording
                    if game_data.get("is_demo_mode", False):
                        print("WARNING: Still in demo mode - this should NOT happen for real games!")
                        # Force it to False for real games to ensure stats are recorded
                        game_data["is_demo_mode"] = False
                        print("Forced is_demo_mode to False to ensure stats are recorded")

                    # Use ONLY event hooks system to prevent duplicates
                    # The event hooks system is the single source of truth for database writes
                    recording_successful = False

                    # First try to use the event hooks system
                    try:
                        import importlib
                        stats_hooks_spec = importlib.util.find_spec('stats_event_hooks')
                        if stats_hooks_spec is not None:
                            print("Using event hooks system for game completion recording")
                            from stats_event_hooks import get_stats_event_hooks
                            stats_hooks = get_stats_event_hooks()

                            # Record the game completion event
                            result = stats_hooks.on_game_completed(game_data)
                            print(f"Event hooks result: {result}")
                            recording_successful = result
                        else:
                            print("Event hooks system not available")
                    except Exception as hooks_e:
                        print(f"Error using event hooks system: {hooks_e}")

                    # Only use direct database recording as fallback if event hooks failed
                    if not recording_successful:
                        try:
                            result = thread_safe_db.record_game_completed(game_data)
                            print(f"Fallback direct database update result: {result}")
                            recording_successful = result
                        except Exception as db_e:
                            print(f"Error with fallback database recording: {db_e}")

                    # Force refresh all data if recording was successful
                    if recording_successful:
                        try:
                            from game_stats_integration import GameStatsIntegration
                            GameStatsIntegration.force_refresh_data()
                            print("Forced refresh of all stats data")
                        except Exception as refresh_e:
                            print(f"Error refreshing stats data: {refresh_e}")

                except Exception as db_e:
                    print(f"Error directly testing database: {db_e}")
                    import traceback
                    traceback.print_exc()

            except Exception as e:
                print(f"Error updating game statistics: {e}")
                import traceback
                traceback.print_exc()

        # CRITICAL FIX: Record game completion BEFORE clearing game state
        # ENHANCED: Capture game state data BEFORE any clearing operations
        called_numbers_before_reset = getattr(self.game, 'called_numbers', []).copy()  # Make a copy
        player_count_before_reset = len(getattr(self.game, 'players', []))
        game_started_before_reset = getattr(self.game, 'game_started', False)

        # Check if we should record a game completion event for a reset game
        should_record_reset = (
            not (self.show_winner_display and self.validation_result and self.player_claim_cartella) and  # No valid winner
            hasattr(self.game, 'game_started') and self.game.game_started and  # Game was actually started
            not is_demo_mode and  # Not in demo mode
            hasattr(self.game, 'players') and len(self.game.players) > 0  # Had players
        )

        if should_record_reset:
            try:
                # Import time module for duration calculation and timestamp
                import time

                # Use the captured data from before reset
                called_numbers = called_numbers_before_reset
                player_count = player_count_before_reset
                game_started = game_started_before_reset

                # Skip recording if no meaningful game activity occurred
                if len(called_numbers) == 0 and player_count == 0 and not game_started:
                    print("Skipping game reset recording - no meaningful game activity occurred")
                    print(f"Called numbers: {len(called_numbers)}, Players: {player_count}, Game started: {game_started}")
                else:
                    print("=" * 80)
                    print("RECORDING GAME RESET EVENT TO STATS (NO WINNER)")
                    print(f"Game was started: {game_started}")
                    print(f"Called numbers count: {len(called_numbers)}")
                    print(f"Player count: {player_count}")
                    print(f"Prize pool: {getattr(self.game, 'prize_pool', 0)}")
                    print("=" * 80)

                    # Calculate game duration
                    game_duration = 0
                    if hasattr(self.game, 'bingo_caller') and hasattr(self.game.bingo_caller, 'start_time'):
                        game_duration = time.time() - self.game.bingo_caller.start_time

                    # Get actual stake amount from game settings - FIXED to use correct source
                    stake_amount = 25  # Updated default to match current settings

                    # Try multiple sources for the bet amount
                    if hasattr(self.game, 'bet_amount') and self.game.bet_amount:
                        stake_amount = self.game.bet_amount
                        print(f"DEBUG: Reset using game.bet_amount: {stake_amount}")
                    elif hasattr(self.game, 'stake_amount') and self.game.stake_amount:
                        stake_amount = self.game.stake_amount
                        print(f"DEBUG: Reset using game.stake_amount: {stake_amount}")
                    else:
                        # Load from game settings file as fallback
                        try:
                            from player_storage import load_game_settings
                            settings = load_game_settings()
                            stake_amount = settings.get('bet_amount', 25)
                            print(f"DEBUG: Reset using settings bet_amount: {stake_amount}")
                        except Exception as e:
                            print(f"DEBUG: Reset could not load settings, using default: {e}")
                            stake_amount = 25

                    # Create game data for statistics with Reset winner
                    game_data = {
                        "winner_name": "Game Reset",
                        "winner_cartella": 0,
                        "claim_type": "Reset",
                        "game_duration": game_duration,
                        "player_count": player_count,
                        "prize_amount": getattr(self.game, 'prize_pool', 0),
                        # FIXED: Always read commission percentage from current settings
                        "commission_percentage": self._get_current_commission_percentage(),
                        "called_numbers": called_numbers,
                        "is_demo_mode": False,  # Force to False since we already checked it's not in demo mode
                        "date_time": time.strftime('%Y-%m-%d %H:%M:%S'),  # Add explicit date_time
                        "stake": stake_amount,  # Add actual stake amount
                        "bet_amount": stake_amount  # For compatibility
                    }

                    print(f"Game data for reset: {game_data}")

                    # Use only ONE recording method to prevent duplicates
                    # Try event hooks system first (preferred method)
                    recording_successful = False

                    try:
                        import importlib
                        stats_hooks_spec = importlib.util.find_spec('stats_event_hooks')
                        if stats_hooks_spec is not None:
                            print("stats_event_hooks module is available for reset event")
                            # Import the stats_event_hooks module
                            from stats_event_hooks import get_stats_event_hooks
                            stats_hooks = get_stats_event_hooks()

                            # Record the game completion event
                            result = stats_hooks.on_game_completed(game_data)
                            print(f"Added game reset event to stats via event hooks: {result}")
                            recording_successful = result
                        else:
                            print("stats_event_hooks module not available")
                    except Exception as hooks_e:
                        print(f"Error using event hooks for reset: {hooks_e}")

                    # Only use direct database recording as fallback
                    if not recording_successful:
                        try:
                            import thread_safe_db
                            result = thread_safe_db.record_game_completed(game_data)
                            print(f"Fallback direct database update result for reset: {result}")
                            recording_successful = result
                        except Exception as db_e:
                            print(f"Error with direct database for reset: {db_e}")

                    # Only use legacy method as last resort
                    if not recording_successful:
                        try:
                            from stats_page import StatsPage
                            StatsPage.update_game_statistics(game_data)
                            print("Game statistics updated for reset game via legacy method")
                            recording_successful = True
                        except Exception as legacy_e:
                            print(f"Error updating game statistics via legacy method: {legacy_e}")

                    if not recording_successful:
                        print("WARNING: Failed to record game reset via any method")

                    # Post a refresh event to update the UI
                    try:
                        import pygame
                        if pygame.get_init():
                            refresh_event = pygame.event.Event(pygame.USEREVENT, {
                                'stats_type': 'refresh_stats',
                                'force': True,
                                'force_reload': True,
                                'source': 'game_reset_event',
                                'timestamp': time.time()
                            })
                            pygame.event.post(refresh_event)
                            print("CRITICAL: Posted refresh_stats event after game reset")
                        else:
                            print("CRITICAL: Pygame not initialized, cannot post refresh event")
                    except Exception as event_e:
                        print(f"CRITICAL: Error posting refresh event after game reset: {event_e}")
                        import traceback
                        traceback.print_exc()

            except Exception as e:
                print(f"Error recording game reset event: {e}")
                import traceback
                traceback.print_exc()

        # Stop calling numbers
        self.game.bingo_caller.stop_calling()

        # Stop any active shuffle sound
        if hasattr(self.game, 'shuffle_sound_channel') and self.game.shuffle_sound_channel is not None:
            try:
                print("Stopping shuffle sound during game reset")
                self.game.shuffle_sound_channel.stop()
                self.game.shuffle_sound_channel = None
            except Exception as e:
                print(f"Error stopping shuffle sound during reset: {e}")

        # Reset shuffle animation state
        if hasattr(self.game, 'shuffle_active'):
            self.game.shuffle_active = False
            self.game.shuffle_offsets = {}
            self.game.current_shuffle_effects = None
            print("Reset shuffle animation state during game reset")

        # Reset game state
        self.is_paused = False
        self.pause_reason = None
        self.pause_reason_number = None
        self.player_claim_cartella = None
        self.show_pause_reason_prompt = False
        self.show_admin_control = False
        self.show_winner_validation = False
        self.show_winner_display = False
        self.show_invalid_claim_display = False
        self.show_missed_winner_display = False

        # Reset timing flags
        self.next_number_called = False
        self.was_next_number_called = False

        # Reset the UI handler's pause prompt flag if it exists
        if hasattr(self.game, 'ui_handler') and hasattr(self.game.ui_handler, 'pause_prompt_just_opened'):
            self.game.ui_handler.pause_prompt_just_opened = False

        # Reset claimed patterns and skipped patterns
        self.claimed_patterns = {}
        self.skipped_patterns = {}

        # Clear winner animation particles if they exist
        if hasattr(self.game, 'ui_handler') and hasattr(self.game.ui_handler, 'winner_particles'):
            self.game.ui_handler.winner_particles = []
            delattr(self.game.ui_handler, 'winner_particles')
            print("Cleared winner animation particles during reset")

        # Reset the audio flags in the UI handler
        # This ensures the sounds will play again for the next claim window
        if hasattr(self.game, 'ui_handler'):
            if hasattr(self.game.ui_handler, '_played_invalid_claim_sound'):
                delattr(self.game.ui_handler, '_played_invalid_claim_sound')
                print("Reset _played_invalid_claim_sound flag during reset")

            if hasattr(self.game.ui_handler, '_played_missed_winner_sound'):
                delattr(self.game.ui_handler, '_played_missed_winner_sound')
                print("Reset _played_missed_winner_sound flag during reset")

        # CRITICAL FIX: Ensure the bingo caller is initialized and stopped before resetting
        if not hasattr(self.game, 'bingo_caller') or self.game.bingo_caller is None:
            print("CRITICAL FIX: Bingo caller not initialized, initializing now")
            try:
                self.setup_bingo_caller()
                print("Successfully initialized bingo caller")
            except Exception as e:
                print(f"Error initializing bingo caller: {e}")
                import traceback
                traceback.print_exc()
                # Continue with reset even if initialization fails

        # Now stop the bingo caller
        if hasattr(self.game, 'bingo_caller') and self.game.bingo_caller:
            try:
                # First check if the bingo caller is active
                if hasattr(self.game.bingo_caller, 'active') and self.game.bingo_caller.active:
                    self.game.bingo_caller.stop_calling()
                    print("CRITICAL FIX: Stopped bingo caller before resetting game state")
                else:
                    print("CRITICAL FIX: Bingo caller is not active, no need to stop it")
            except Exception as e:
                print(f"Error stopping bingo caller: {e}")
                import traceback
                traceback.print_exc()
                # Continue with reset even if stopping the bingo caller fails

        # CRITICAL FIX: Directly reset game state variables
        print("CRITICAL FIX: Directly resetting game state variables")
        if hasattr(self.game, 'called_numbers'):
            self.game.called_numbers = []
            print("CRITICAL FIX: Reset called_numbers to empty list")

        if hasattr(self.game, 'current_number'):
            self.game.current_number = None
            print("CRITICAL FIX: Reset current_number to None")

        # Reset main game state
        self.game.game_started = False
        self.game.called_numbers = []
        self.game.current_number = None

        # Reset demo mode flag - it will be set correctly when starting a new game
        if hasattr(self.game, 'is_demo_mode'):
            self.game.is_demo_mode = False

        # CRITICAL FIX: Force a redraw of the screen to ensure UI is updated
        if hasattr(self.game, 'force_redraw'):
            self.game.force_redraw = True
            print("CRITICAL FIX: Forced redraw during reset_game")

        # DO NOT clear all JSON data - preserve "remember for next session" functionality
        # Only clear player data, not UI state that users want to remember
        print("Preserving UI state for 'remember for next session' functionality")
        try:
            from player_storage import save_players_to_json
            save_players_to_json([])  # Clear only player data, preserve UI state
            print("Cleared player data while preserving UI state")
        except Exception as e:
            print(f"Error clearing player data: {str(e)}")

        # Ensure the bingo boards get regenerated
        if hasattr(self.game, 'ensure_boards_exist'):
            self.game.ensure_boards_exist()

        # Clear players data by saving an empty list
        from player_storage import save_players_to_json
        # Clear the players list in memory
        self.game.players = []
        # Save empty players list to file
        save_players_to_json(self.game.players)

        # Reset session data
        try:
            import os
            import json
            import datetime

            # Preserve the prize pool and manual override flag if set
            preserved_prize_pool = None
            preserved_manual_override = False

            if hasattr(self.game, 'prize_pool_manual_override') and self.game.prize_pool_manual_override:
                preserved_prize_pool = self.game.prize_pool
                preserved_manual_override = True
                print(f"CRITICAL FIX: Preserving manually set prize pool: {preserved_prize_pool} ETB")

            # Create default session data
            session_data = {
                "session_info": {
                    "start_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "duration_seconds": 0,
                    "players_added": 0,
                    "total_bets_etb": 0,
                    "prize_pool_etb": preserved_prize_pool if preserved_manual_override else getattr(self.game, 'initial_prize_pool', 0),
                    "prize_pool_manual_override": preserved_manual_override
                },
                "players": []
            }

            # Save to session file
            os.makedirs('data', exist_ok=True)
            filepath = os.path.join('data', 'current_session.json')
            with open(filepath, 'w') as f:
                json.dump(session_data, f, indent=4)

            # Also update the game settings to preserve the prize pool
            if preserved_manual_override and preserved_prize_pool is not None:
                try:
                    from player_storage import save_game_settings
                    settings = {
                        'commission_percentage': getattr(self.game, 'commission_percentage', 20.0),
                        'bet_amount': getattr(self.game, 'bet_amount', 50),
                        'prize_pool': preserved_prize_pool,
                        'prize_pool_manual_override': preserved_manual_override
                    }
                    save_game_settings(settings)
                    print(f"CRITICAL FIX: Saved game settings with preserved prize pool: {settings}")
                except Exception as e:
                    print(f"Error saving game settings with preserved prize pool: {e}")

        except Exception as e:
            print(f"Error resetting session data: {str(e)}")

        # Update prize pool - skip calculation if manual override is set
        if hasattr(self.game, 'prize_pool_manual_override') and self.game.prize_pool_manual_override:
            print(f"CRITICAL FIX: Maintaining manually set prize pool: {self.game.prize_pool} ETB")
        elif hasattr(self.game, 'calculate_prize_pool'):
            self.game.calculate_prize_pool()

        # Display confirmation message
        if hasattr(self.game, 'message'):
            self.game.message = "Game reset and data cleared successfully"
            self.game.message_type = "success"
            self.game.message_timer = 180

        # CRITICAL FIX: Always return to board selection when reset is confirmed
        # This ensures the reset confirmation button always works properly
        print("CRITICAL FIX: Always returning to board selection after reset")

        # CRITICAL FIX: Force a redraw of the screen to ensure UI is updated
        if hasattr(self.game, 'force_redraw'):
            self.game.force_redraw = True
            print("CRITICAL FIX: Forced redraw before returning to board selection")

        # CRITICAL FIX: Ensure we're not in demo mode
        if hasattr(self.game, 'is_demo_mode'):
            self.game.is_demo_mode = False
            print("CRITICAL FIX: Explicitly set is_demo_mode to False before returning to board selection")

        # CRITICAL FIX: Force a screen update to ensure UI changes are visible
        try:
            pygame.display.flip()
            print("CRITICAL FIX: Forced screen update before returning to board selection")
        except Exception as e:
            print(f"Error forcing screen update: {e}")

        # CRITICAL FIX: Add a small delay to ensure the screen is updated
        try:
            import time
            time.sleep(0.1)
            print("CRITICAL FIX: Added small delay before returning to board selection")
        except Exception as e:
            print(f"Error adding delay: {e}")

        # Return a flag indicating we should go back to board selection
        return True

    def record_automatic_winner(self, winner_cartella):
        """Record an automatically detected winner without manual claim"""
        try:
            import time

            # Check if we're in demo mode
            is_demo_mode = getattr(self.game, 'is_demo_mode', False)
            if is_demo_mode:
                print(f"Skipping automatic winner recording for cartella {winner_cartella} - in demo mode")
                return

            # Get the winning pattern for this cartella
            winning_pattern = "Auto-Detected"
            if hasattr(self.game, 'bingo_logic'):
                card = self.game.bingo_logic.get_card_for_player(winner_cartella)
                if card and hasattr(card, 'winning_pattern'):
                    winning_pattern = card.winning_pattern or "Auto-Detected"

            # Calculate game duration
            game_duration = 0
            if hasattr(self.game, 'bingo_caller') and hasattr(self.game.bingo_caller, 'start_time'):
                game_duration = time.time() - self.game.bingo_caller.start_time

            # Get actual stake amount from game settings - FIXED to use correct source
            stake_amount = 25  # Updated default to match current settings

            # Try multiple sources for the bet amount
            if hasattr(self.game, 'bet_amount') and self.game.bet_amount:
                stake_amount = self.game.bet_amount
                print(f"DEBUG: Auto-winner using game.bet_amount: {stake_amount}")
            elif hasattr(self.game, 'stake_amount') and self.game.stake_amount:
                stake_amount = self.game.stake_amount
                print(f"DEBUG: Auto-winner using game.stake_amount: {stake_amount}")
            else:
                # Load from game settings file as fallback
                try:
                    from player_storage import load_game_settings
                    settings = load_game_settings()
                    stake_amount = settings.get('bet_amount', 25)
                    print(f"DEBUG: Auto-winner using settings bet_amount: {stake_amount}")
                except Exception as e:
                    print(f"DEBUG: Auto-winner could not load settings, using default: {e}")
                    stake_amount = 25

            # Create game data for statistics
            game_data = {
                "winner_name": f"Cartella #{winner_cartella}",
                "winner_cartella": winner_cartella,
                "claim_type": winning_pattern,
                "game_duration": game_duration,
                "player_count": len(self.game.players),
                "prize_amount": getattr(self.game, 'prize_pool', 0),
                "commission_percentage": getattr(self.game, 'commission_percentage', 20),
                "called_numbers": self.game.called_numbers,
                "is_demo_mode": False,
                "date_time": time.strftime('%Y-%m-%d %H:%M:%S'),
                "stake": stake_amount,
                "bet_amount": stake_amount,
                "completion_type": "auto_detected_winner"
            }

            print("=" * 80)
            print(f"RECORDING AUTO-DETECTED WINNER: Cartella #{winner_cartella}")
            print(f"Game data: {game_data}")
            print("=" * 80)

            # Record using the same system as manual claims
            recording_successful = False

            # Try event hooks system first
            try:
                import importlib
                stats_hooks_spec = importlib.util.find_spec('stats_event_hooks')
                if stats_hooks_spec is not None:
                    from stats_event_hooks import get_stats_event_hooks
                    stats_hooks = get_stats_event_hooks()
                    result = stats_hooks.on_game_completed(game_data)
                    print(f"Auto-winner event hooks result: {result}")
                    recording_successful = result
            except Exception as hooks_e:
                print(f"Error using event hooks for auto-winner: {hooks_e}")

            # Fallback to direct database recording
            if not recording_successful:
                try:
                    import thread_safe_db
                    result = thread_safe_db.record_game_completed(game_data)
                    print(f"Auto-winner direct database result: {result}")
                    recording_successful = result
                except Exception as db_e:
                    print(f"Error with direct database for auto-winner: {db_e}")

            # Force refresh if successful
            if recording_successful:
                try:
                    from game_stats_integration import GameStatsIntegration
                    GameStatsIntegration.force_refresh_data()
                    print("Forced refresh after auto-winner recording")
                except Exception as refresh_e:
                    print(f"Error refreshing after auto-winner: {refresh_e}")

        except Exception as e:
            print(f"Error in record_automatic_winner: {e}")
            import traceback
            traceback.print_exc()

    def handle_pause_reason(self, reason_number):
        """Handle the pause reason input from user"""
        # Convert to integer if it's a string
        try:
            reason_number = int(reason_number)
        except ValueError:
            return False

        self.pause_reason_number = reason_number
        self.show_pause_reason_prompt = False

        # CRITICAL FIX: Reset the checking_next_claim flag when handling a new pause reason
        # This ensures we're not in "checking multiple claims" mode for the first claim
        if hasattr(self, 'checking_next_claim'):
            self.checking_next_claim = False
            print("Reset checking_next_claim flag for new pause reason")

        # Handle player claims for numbers 1-1200
        if 1 <= reason_number <= 1200:
            # Player claiming a win (extended range from 100 to 1200)
            self.pause_reason = GamePauseReason.PLAYER_CLAIM
            self.player_claim_cartella = reason_number

            # Store the current value of next_number_called before resetting it
            # This will be used to determine if the claim was made after the next number was called
            self.was_next_number_called = self.next_number_called
            
            # Set the late_claim flag based on was_next_number_called for consistency
            # This centralizes the late claim detection logic
            self.late_claim = self.was_next_number_called
            
            # Log the state for debugging with improved clarity
            if self.was_next_number_called:
                print(f"IMPORTANT: Claim made AFTER next number was called - marking as late claim")
                print(f"Late claim flags set: was_next_number_called={self.was_next_number_called}, late_claim={self.late_claim}")
            else:
                print(f"Claim made BEFORE next number was called - this could be a valid claim")
                print(f"Late claim flags cleared: was_next_number_called={self.was_next_number_called}, late_claim={self.late_claim}")

            # Reset the next_number_called flag when a player makes a claim
            # This is needed for the UI flow but we'll use was_next_number_called and late_claim for validation
            self.next_number_called = False

            # Reset the last_number_time to current time
            # This ensures that the time-based check doesn't incorrectly mark claims as late
            self.last_number_time = pygame.time.get_ticks()
            print("Reset next_number_called flag and last_number_time for player claim")

            self.show_winner_validation = True
            self.validate_player_claim(reason_number)
            return True
        else:
            # For any other input (including 0), set admin mode
            self.pause_reason = GamePauseReason.ADMIN
            self.pause_reason_number = reason_number
            # Keep the pause reason window open
            self.show_pause_reason_prompt = True
            print(f"Admin mode with pause_reason_number = {self.pause_reason_number}")
            return True

    def handle_claim_click(self, pos):
        """Handle clicks specifically for the claim verification window"""
        # Only process claim clicks if the game is actually paused for a claim
        if not hasattr(self, 'show_winner_validation') or not self.show_winner_validation:
            return False

        # Define clickable areas based on validation display
        if hasattr(self, 'validation_buttons'):
            for button_name, button_rect in self.validation_buttons.items():
                if button_rect.collidepoint(pos):
                    # Handle different button actions
                    if button_name == "continue":
                        self.close_validation_display()
                        return True
                    elif button_name == "penalize":
                        if hasattr(self, 'player_claim_cartella'):
                            self.penalise_player(self.player_claim_cartella)
                        return True
                    elif button_name == "next_claim":
                        self.check_next_claim()
                        return True

        return False

    def validate_player_claim(self, cartella_number):
        """Validate if the player's claim is legitimate"""
        try:
            print("=" * 100)
            print(f"VALIDATION DEBUG: Starting validation for cartella {cartella_number}")
            print("=" * 100)

            # CRITICAL FIX: Reset ALL display flags at the start of validation
            # This prevents multiple patterns from setting conflicting display flags
            self.show_winner_display = False
            self.show_missed_winner_display = False
            self.show_invalid_claim_display = False
            self.show_winner_validation = False
            print("CRITICAL FIX: Reset all display flags at start of validation")

            # Reset claim type and related fields
            self.claim_type = None
            self.winning_number = None
            self.last_winning_chance_number = None

            # AUDIO FIX: Reset audio tracking flags to prevent conflicts
            self._audio_played_for_this_claim = False
            self._validation_call_count = getattr(self, '_validation_call_count', 0) + 1
            print(f"AUDIO FIX: Reset audio tracking flags (validation call #{self._validation_call_count})")
            print(f"This is validation call #{self._validation_call_count} for cartella {cartella_number}")

            # Check if cartella is registered
            is_registered = False
            if hasattr(self.game, 'players'):
                for player in self.game.players:
                    player_cartella = None
                    # Try different possible attribute names for cartella number
                    for attr_name in ['cartela_no', 'cartella_no', 'cartella_number', 'card_number', 'number']:
                        if hasattr(player, attr_name):
                            player_cartella = getattr(player, attr_name)
                            break
                    
                    if player_cartella is not None and str(player_cartella) == str(cartella_number):
                        is_registered = True
                        print(f"Found registered cartella: {cartella_number}")
                        break
            
            # Handle unregistered cartella
            if not is_registered:
                print(f"UNREGISTERED CARTELLA: Cartella {cartella_number} is not registered")
                self.validation_result = False
                self.invalid_claim_reason = f"Cartella #{cartella_number} is not registered"
                self.claim_type = "unregistered_cartella"
                self.show_invalid_claim_display = True
                self.show_winner_validation = False
                
                # Play warning sound
                try:
                    if hasattr(self.game, 'play_warning_sound'):
                        self.game.play_warning_sound()
                except Exception as sound_e:
                    print(f"Error playing warning sound: {sound_e}")
                
                return False

            # Get the current number and call count
            current_number = None
            call_count = 0
            if hasattr(self.game, 'current_number'):
                current_number = self.game.current_number
            if hasattr(self.game, 'called_numbers'):
                call_count = len(self.game.called_numbers)
            
            print(f"VALIDATION DEBUG: Current number: {current_number}, Call count: {call_count}")
            
            # Get the card for this player
            try:
                if not hasattr(self.game, 'bingo_logic'):
                    print("ERROR: bingo_logic not found in game object")
                    raise Exception("Bingo logic not initialized")
                    
                card = self.game.bingo_logic.get_card_for_player(cartella_number)
                if not card:
                    print(f"ERROR: Could not get card for cartella {cartella_number}")
                    raise Exception(f"Card not found for cartella {cartella_number}")
                    
                print(f"VALIDATION DEBUG: Successfully got card for cartella {cartella_number}")
                
            except Exception as card_e:
                print(f"ERROR getting card for player {cartella_number}: {card_e}")
                self.validation_result = False
                self.invalid_claim_reason = f"Error loading card for cartella #{cartella_number}"
                self.claim_type = "card_error"
                self.show_invalid_claim_display = True
                self.show_winner_validation = False
                
                try:
                    if hasattr(self.game, 'play_warning_sound'):
                        self.game.play_warning_sound()
                except Exception as sound_e:
                    print(f"Error playing warning sound: {sound_e}")
                
                return False
            
            # Check for winning patterns
            try:
                # CRITICAL FIX: Mark all called numbers on the card before checking patterns
                print(f"VALIDATION DEBUG: Marking called numbers on card before pattern check")
                print(f"VALIDATION DEBUG: Called numbers: {self.game.called_numbers}")
                
                # Mark all called numbers on the card
                for number in self.game.called_numbers:
                    card.mark_number(number)
                
                print(f"VALIDATION DEBUG: Card marked numbers: {card.marked}")
                
                has_winning_patterns = card.check_winning_patterns(self.game.called_numbers)
                print(f"VALIDATION DEBUG: Card has winning patterns: {has_winning_patterns}")
                
                if has_winning_patterns:
                    # Get all winning patterns
                    all_patterns = []
                    if hasattr(card, 'winning_patterns') and card.winning_patterns:
                        all_patterns = card.winning_patterns
                    elif hasattr(card, 'winning_pattern') and card.winning_pattern:
                        all_patterns = [card.winning_pattern]
                    
                    print(f"VALIDATION DEBUG: Found patterns: {all_patterns}")
                    
                    # Check each pattern against our new validation rules
                    for pattern_name in all_patterns:
                        try:
                            pattern_numbers = self.get_winning_pattern_numbers(card, pattern_name)
                            print(f"VALIDATION DEBUG: Pattern {pattern_name} numbers: {pattern_numbers}")
                            
                            # NEW LOGIC: Always accept valid patterns if current number is in pattern
                            # Check if current number is in the pattern first
                            if current_number in pattern_numbers:
                                print(f"VALID CLAIM: Pattern {pattern_name} found and current number {current_number} is in pattern")
                                
                                # Check if this qualifies for bonus (early claim)
                                is_early_claim = self.pattern_bonus_system.is_valid_pattern_claim(pattern_name, call_count)
                                
                                if is_early_claim:
                                    print(f"EARLY CLAIM: Pattern {pattern_name} qualifies for bonus (call count: {call_count})")
                                    self.claim_type = "valid"
                                else:
                                    print(f"LATE CLAIM: Pattern {pattern_name} is valid but no bonus (call count: {call_count})")
                                    self.claim_type = "late_valid"  # New claim type for late but valid claims
                                
                                # Set up for valid winner display
                                self.validation_result = True
                                self.winner_pattern = pattern_name
                                self.winning_patterns = [pattern_name]
                                self.show_winner_display = True
                                self.show_missed_winner_display = False
                                self.show_invalid_claim_display = False
                                self.show_winner_validation = False
                                
                                # Calculate and apply pattern bonus ONLY for early claims
                                if is_early_claim:
                                    try:
                                        print(f"VALIDATION DEBUG: Calculating pattern bonus for early claim {pattern_name}")
                                        bonus_info = self.calculate_pattern_bonus(cartella_number, pattern_name)
                                        print(f"VALIDATION DEBUG: Pattern bonus calculated successfully: {bonus_info}")
                                    except Exception as bonus_e:
                                        print(f"ERROR calculating pattern bonus: {bonus_e}")
                                        traceback.print_exc()
                                        # Continue without bonus - don't let this crash the validation
                                else:
                                    print(f"VALIDATION DEBUG: Late claim - no bonus applied, fallback to normal prize")
                                    # For late claims, we don't calculate bonus - just normal prize

                                # Store winner information for display
                                try:
                                    print(f"VALIDATION DEBUG: Setting up winner display for cartella {cartella_number}")
                                    self.winner_cartella = cartella_number
                                    self.winner_pattern = pattern_name
                                    self.winning_patterns = [pattern_name]
                                    print(f"VALIDATION DEBUG: Winner display setup complete")
                                except Exception as display_e:
                                    print(f"ERROR setting up winner display: {display_e}")
                                    traceback.print_exc()

                                # Play winner sound
                                try:
                                    print(f"VALIDATION DEBUG: Attempting to play winner sound")
                                    if hasattr(self.game, 'play_winner_sound'):
                                        result = self.game.play_winner_sound()
                                        print(f"VALIDATION DEBUG: Winner sound played successfully - result: {result}")
                                    else:
                                        print(f"VALIDATION DEBUG: play_winner_sound method not found on game object")
                                except Exception as sound_e:
                                    print(f"ERROR playing winner sound: {sound_e}")
                                    traceback.print_exc()
                                    # Continue without sound - don't let this crash the validation

                                # Add to winners and claimed patterns
                                try:
                                    print(f"VALIDATION DEBUG: Adding cartella {cartella_number} to winners list")
                                    
                                    # Ensure bingo_logic exists
                                    if not hasattr(self.game, 'bingo_logic'):
                                        print(f"ERROR: bingo_logic not found on game object")
                                        raise Exception("bingo_logic not initialized")
                                    
                                    # Ensure winners list exists
                                    if not hasattr(self.game.bingo_logic, 'winners'):
                                        print(f"VALIDATION DEBUG: Creating winners list on bingo_logic")
                                        self.game.bingo_logic.winners = []
                                    
                                    # Add to winners if not already there
                                    if cartella_number not in self.game.bingo_logic.winners:
                                        self.game.bingo_logic.winners.append(cartella_number)
                                        print(f"VALIDATION DEBUG: Added cartella {cartella_number} to winners list")
                                    else:
                                        print(f"VALIDATION DEBUG: Cartella {cartella_number} already in winners list")

                                    # Ensure claimed_patterns dict exists
                                    if not hasattr(self, 'claimed_patterns'):
                                        print(f"VALIDATION DEBUG: Creating claimed_patterns dict")
                                        self.claimed_patterns = {}
                                    
                                    if cartella_number not in self.claimed_patterns:
                                        self.claimed_patterns[cartella_number] = []

                                    # Add the winning pattern to claimed patterns
                                    pattern_already_claimed = False
                                    for claimed_pattern in self.claimed_patterns.get(cartella_number, []):
                                        if sorted(claimed_pattern) == sorted(pattern_numbers):
                                            pattern_already_claimed = True
                                            break
                                    
                                    if not pattern_already_claimed:
                                        self.claimed_patterns[cartella_number] = self.claimed_patterns.get(cartella_number, [])
                                        self.claimed_patterns[cartella_number].append(pattern_numbers)
                                        print(f"VALIDATION DEBUG: Added pattern {pattern_name} to claimed patterns for cartella {cartella_number}")
                                    else:
                                        print(f"VALIDATION DEBUG: Pattern {pattern_name} already claimed for cartella {cartella_number}")
                                        
                                except Exception as winner_e:
                                    print(f"ERROR updating winner lists: {winner_e}")
                                    traceback.print_exc()
                                    # Continue - don't let this crash the validation

                                print(f"VALIDATION DEBUG: Valid claim processing completed successfully")
                                return True
                            else:
                                print(f"INVALID CLAIM: Current number {current_number} not in pattern {pattern_name}")
                                # Continue checking other patterns
                        except Exception as pattern_e:
                            print(f"Error processing pattern {pattern_name}: {pattern_e}")
                            continue
                    
                    # If we get here, no valid patterns were found
                    # Check if there are any patterns that would be valid if they were enabled
                    has_disabled_pattern = False
                    try:
                        for pattern_name in all_patterns:
                            pattern_setting_key = f"enable_{pattern_name.lower().replace(' ', '_').replace('(', '').replace(')', '').replace('-', '_').replace('/', '_')}"
                            from settings_manager import SettingsManager
                            settings = SettingsManager()
                            pattern_enabled = settings.get_setting("pattern_bonuses", pattern_setting_key, True)
                            
                            if not pattern_enabled:
                                has_disabled_pattern = True
                                print(f"Found disabled pattern: {pattern_name}")
                                break
                    except Exception as settings_e:
                        print(f"Error checking pattern settings: {settings_e}")
                    
                    # If there are disabled patterns, show a specific message
                    if has_disabled_pattern:
                        self.validation_result = False
                        self.invalid_claim_reason = "Pattern is disabled in settings"
                        self.claim_type = "disabled_pattern"
                        self.show_invalid_claim_display = True
                        self.show_winner_validation = False
                        
                        # Play warning sound
                        try:
                            if hasattr(self.game, 'play_warning_sound'):
                                self.game.play_warning_sound()
                        except Exception as sound_e:
                            print(f"Error playing warning sound: {sound_e}")
                        
                        return False
                    
                    # OLD LOGIC REMOVED: This was overriding the new late_valid claim logic
                    # The new logic above already handles late claims properly by:
                    # 1. Accepting valid patterns if current number is in pattern
                    # 2. Setting claim_type to "late_valid" for late but valid claims
                    # 3. Only rejecting claims where current number is NOT in pattern
                    print(f"VALIDATION DEBUG: Skipping old late pattern logic - new logic already handled this")
                        
            except Exception as pattern_check_e:
                print(f"ERROR checking winning patterns: {pattern_check_e}")
                traceback.print_exc()
                self.validation_result = False
                self.invalid_claim_reason = f"Error checking patterns for cartella #{cartella_number}"
                self.claim_type = "pattern_check_error"
                self.show_invalid_claim_display = True
                self.show_winner_validation = False
                
                try:
                    if hasattr(self.game, 'play_warning_sound'):
                        self.game.play_warning_sound()
                except Exception as sound_e:
                    print(f"Error playing warning sound: {sound_e}")
                
                return False
            
            # Store current number for reference
            if hasattr(self.game, 'current_number'):
                self.winning_number = self.game.current_number
                print(f"VALIDATION DEBUG: Current number is {self.winning_number}")
            
            # If we get here, no valid patterns were found at all
            self.validation_result = False
            self.invalid_claim_reason = "No valid winning pattern found"
            self.claim_type = "no_pattern"
            self.show_invalid_claim_display = True
            self.show_winner_validation = False
            
            # Play warning sound
            try:
                if hasattr(self.game, 'play_warning_sound'):
                    self.game.play_warning_sound()
            except Exception as sound_e:
                print(f"Error playing warning sound: {sound_e}")
            
            return False
            
        except Exception as e:
            print(f"CRITICAL ERROR in validate_player_claim: {e}")
            traceback.print_exc()
            
            # Set safe fallback state
            self.validation_result = False
            self.invalid_claim_reason = f"System error validating cartella #{cartella_number}"
            self.claim_type = "system_error"
            self.show_invalid_claim_display = True
            self.show_winner_validation = False
            self.show_winner_display = False
            self.show_missed_winner_display = False
            
            # Try to play warning sound
            try:
                if hasattr(self.game, 'play_warning_sound'):
                    self.game.play_warning_sound()
            except Exception as sound_e:
                print(f"Error playing warning sound after critical error: {sound_e}")
            
            return False

    def get_winning_pattern_numbers(self, card, pattern_name):
        """
        Get the numbers in a specific winning pattern on a card

        Args:
            card: The bingo card object
            pattern_name: The name of the winning pattern

        Returns:
            list: List of numbers in the winning pattern
        """
        if not pattern_name:
            return []

        # CRITICAL FIX: Use pattern_utils to normalize the pattern name
        try:
            import pattern_utils
            normalized_name = pattern_utils.normalize_pattern_name(pattern_name)
            print(f"CRITICAL FIX: Normalized pattern name '{pattern_name}' to '{normalized_name}'")

            # If the pattern name was normalized, try to get the pattern cells directly from pattern_utils
            if normalized_name != pattern_name:
                pattern_cells = pattern_utils.get_pattern_cells_by_name(normalized_name)
                if pattern_cells:
                    # Convert pattern cells to numbers using the card's grid
                    numbers = []
                    if hasattr(card, 'grid'):
                        for row, col in pattern_cells:
                            num = card.grid[col][row]  # Note: grid is column-major, so we use [col][row]
                            if num != 0:  # Skip free space
                                numbers.append(num)
                        print(f"CRITICAL FIX: Got numbers for pattern '{normalized_name}' using pattern_utils: {numbers}")
                        return numbers
        except Exception as e:
            print(f"CRITICAL FIX: Error using pattern_utils: {e}")
            # Continue with the original implementation as fallback

        # Check if we're using the grid or numbers attribute
        if hasattr(card, 'grid'):
            grid_attr = 'grid'
        elif hasattr(card, 'numbers'):
            grid_attr = 'numbers'
        else:
            return []

        grid = getattr(card, grid_attr)
        numbers = []

        # Handle different pattern types
        if "Row" in pattern_name:
            # Extract row number (1-5)
            try:
                row_num = int(pattern_name.split()[-1]) - 1  # Convert to 0-based index
                if 0 <= row_num < 5:
                    # Get all numbers in this row
                    for col in range(5):
                        # Skip the FREE space in the middle
                        if row_num == 2 and col == 2:
                            continue
                        num = grid[col][row_num] if grid_attr == 'grid' else grid[row_num][col]
                        if num != 0:  # Skip free space
                            numbers.append(num)
            except (ValueError, IndexError):
                pass

        elif "Column" in pattern_name:
            # Extract column number (1-5)
            try:
                col_num = int(pattern_name.split()[-1]) - 1  # Convert to 0-based index
                if 0 <= col_num < 5:
                    # Get all numbers in this column
                    for row in range(5):
                        # Skip the FREE space in the middle
                        if row == 2 and col_num == 2:
                            continue
                        num = grid[col_num][row] if grid_attr == 'grid' else grid[row][col_num]
                        if num != 0:  # Skip free space
                            numbers.append(num)
            except (ValueError, IndexError):
                pass

        elif "Diagonal" in pattern_name:
            # CRITICAL FIX: Improved diagonal pattern detection with better pattern name matching
            # This handles both full pattern names and simplified versions

            # Log the pattern name for debugging
            print(f"CRITICAL FIX: Processing diagonal pattern: {pattern_name}")

            # Check for main diagonal (top-left to bottom-right)
            if "top-left" in pattern_name.lower() or (
                "diagonal" in pattern_name.lower() and
                not "top-right" in pattern_name.lower() and
                not "bottom-left" in pattern_name.lower()
            ):
                print(f"CRITICAL FIX: Detected main diagonal (top-left to bottom-right)")
                # Main diagonal (top-left to bottom-right)
                for i in range(5):
                    # Skip the FREE space in the middle
                    if i == 2:
                        continue
                    num = grid[i][i] if grid_attr == 'grid' else grid[i][i]
                    if num != 0:  # Skip free space
                        numbers.append(num)
                print(f"CRITICAL FIX: Main diagonal numbers: {numbers}")

            # Check for anti-diagonal (top-right to bottom-left)
            elif "top-right" in pattern_name.lower() or "bottom-left" in pattern_name.lower():
                print(f"CRITICAL FIX: Detected anti-diagonal (top-right to bottom-left)")
                # Anti-diagonal (top-right to bottom-left)
                for i in range(5):
                    # Skip the FREE space in the middle
                    if i == 2:
                        continue
                    num = grid[i][4-i] if grid_attr == 'grid' else grid[i][4-i]
                    if num != 0:  # Skip free space
                        numbers.append(num)
                print(f"CRITICAL FIX: Anti-diagonal numbers: {numbers}")

            # If we can't determine which diagonal, try both
            else:
                print(f"CRITICAL FIX: Could not determine which diagonal, trying both")

                # Try main diagonal first
                main_diagonal = []
                for i in range(5):
                    # Skip the FREE space in the middle
                    if i == 2:
                        continue
                    num = grid[i][i] if grid_attr == 'grid' else grid[i][i]
                    if num != 0:  # Skip free space
                        main_diagonal.append(num)

                # Try anti-diagonal
                anti_diagonal = []
                for i in range(5):
                    # Skip the FREE space in the middle
                    if i == 2:
                        continue
                    num = grid[i][4-i] if grid_attr == 'grid' else grid[i][4-i]
                    if num != 0:  # Skip free space
                        anti_diagonal.append(num)

                # Use the one that has more numbers (more likely to be the correct one)
                if len(main_diagonal) >= len(anti_diagonal):
                    numbers = main_diagonal
                    print(f"CRITICAL FIX: Using main diagonal: {numbers}")
                else:
                    numbers = anti_diagonal
                    print(f"CRITICAL FIX: Using anti-diagonal: {numbers}")

        elif "Four Corners" in pattern_name:
            # Four corners pattern
            corners = [(0, 0), (0, 4), (4, 0), (4, 4)]
            for col, row in corners:  # Note: grid is column-major, so we use [col][row]
                num = grid[col][row] if grid_attr == 'grid' else grid[row][col]
                if num != 0:  # Skip empty cells
                    numbers.append(num)

        elif "Blackout" in pattern_name or "Coverall" in pattern_name:
            # All numbers on the card except the FREE space
            for col in range(5):
                for row in range(5):
                    # Skip the FREE space in the middle
                    if row == 2 and col == 2:
                        continue
                    num = grid[col][row] if grid_attr == 'grid' else grid[row][col]
                    if num != 0:  # Skip empty cells
                        numbers.append(num)

        return numbers

    def is_cartella_registered(self, cartella_number):
        """Check if the cartella number is registered in the game"""
        # Look through the players list to find the cartella
        for player in self.game.players:
            if player.cartela_no == cartella_number:
                return True
        return False

    def close_validation_display(self):
        """Close the validation display and handle next steps"""
        print("CLOSE_VALIDATION_DISPLAY called - This should safely close the validation window")

        # CRITICAL FIX: Verify that the game is actually started before resuming
        # This prevents accidentally resetting the game when it's not actually running
        if not hasattr(self.game, 'game_started') or not self.game.game_started:
            print("CRITICAL ERROR: Attempted to resume a game that hasn't been started!")
            print("This could cause the game to reset instead of resume.")
            # Just close the windows without resuming
            self.show_winner_display = False
            self.show_invalid_claim_display = False
            self.show_missed_winner_display = False
            return

        # CRITICAL FIX: Verify that the game is actually paused before resuming
        if not self.is_paused:
            print("CRITICAL ERROR: Attempted to resume a game that isn't paused!")
            print("This could cause the game to reset instead of resume.")
            # Just close the windows without resuming
            self.show_winner_display = False
            self.show_invalid_claim_display = False
            self.show_missed_winner_display = False
            return

        if self.show_winner_display:
            # Just close the winner display and resume the game
            # This allows checking multiple winners without resetting the game
            self.show_winner_display = False

            # Clear winner animation particles if they exist
            if hasattr(self.game, 'ui_handler') and hasattr(self.game.ui_handler, 'winner_particles'):
                self.game.ui_handler.winner_particles = []
                delattr(self.game.ui_handler, 'winner_particles')
                print("Cleared winner animation particles")

            # CRITICAL FIX: Add additional safety check before resuming
            try:
                # Resume the game so players can continue playing
                # Use the new resume method with audio if available
                if hasattr(self.game, 'resume_game_with_audio'):
                    self.game.resume_game_with_audio()
                else:
                    # Fallback to old method
                    self.resume_game()
            except Exception as e:
                print(f"ERROR resuming game: {e}")
                print("This might indicate a deeper issue with the game state.")
                # Don't let exceptions propagate - we want to keep the game running
            return

        if self.show_invalid_claim_display:
            # If it was an invalid claim, resume the game
            self.show_invalid_claim_display = False

            # Reset the _played_invalid_claim_sound flag in the UI handler
            # This ensures the sound will play again the next time the window is shown
            if hasattr(self.game, 'ui_handler') and hasattr(self.game.ui_handler, '_played_invalid_claim_sound'):
                delattr(self.game.ui_handler, '_played_invalid_claim_sound')
                print("Reset _played_invalid_claim_sound flag")

            # CRITICAL FIX: Add additional safety check before resuming
            try:
                # Use the new resume method with audio if available
                if hasattr(self.game, 'resume_game_with_audio'):
                    self.game.resume_game_with_audio()
                else:
                    # Fallback to old method
                    self.resume_game()
            except Exception as e:
                print(f"ERROR resuming game: {e}")
                print("This might indicate a deeper issue with the game state.")
                # Don't let exceptions propagate - we want to keep the game running
            return

        if self.show_missed_winner_display:
            # If it was a missed winner, resume the game
            self.show_missed_winner_display = False

            # Clear winner animation particles if they exist (for valid winners)
            if hasattr(self.game, 'ui_handler') and hasattr(self.game.ui_handler, 'winner_particles'):
                self.game.ui_handler.winner_particles = []
                delattr(self.game.ui_handler, 'winner_particles')
                print("Cleared winner animation particles from missed winner display")

            # Reset the _played_missed_winner_sound flag in the UI handler
            # This ensures the sound will play again the next time the window is shown
            if hasattr(self.game, 'ui_handler') and hasattr(self.game.ui_handler, '_played_missed_winner_sound'):
                delattr(self.game.ui_handler, '_played_missed_winner_sound')
                print("Reset _played_missed_winner_sound flag")

            # CRITICAL FIX: Add additional safety check before resuming
            try:
                # Use the new resume method with audio if available
                if hasattr(self.game, 'resume_game_with_audio'):
                    self.game.resume_game_with_audio()
                else:
                    # Fallback to old method
                    self.resume_game()
            except Exception as e:
                print(f"ERROR resuming game: {e}")
                print("This might indicate a deeper issue with the game state.")
                # Don't let exceptions propagate - we want to keep the game running
            return

    def penalise_player(self, cartella_number):
        """
        Penalise a player by removing them from the current game session

        Args:
            cartella_number: The cartella number to penalise

        Returns:
            bool: True if successful, False otherwise
        """
        # Check if the cartella is registered
        if not self.is_cartella_registered(cartella_number):
            return False

        # Find the player in the players list
        player_index = None
        for i, player in enumerate(self.game.players):
            if player.cartela_no == cartella_number:
                player_index = i
                break

        if player_index is None:
            return False

        # Audio is now played directly in the UI handler when the button is clicked
        # No need to play audio here

        # Remove the player from the current game session
        self.game.players.pop(player_index)

        # Log the penalisation
        print(f"Player with cartella {cartella_number} penalised and removed from game session")

        # Show a message to the user
        if hasattr(self.game, 'message'):
            self.game.message = f"Player with cartella {cartella_number} penalised"
            self.game.message_type = "warning"
            self.game.message_timer = 180  # 3 seconds at 60 FPS

        # CRITICAL FIX: Close all claim displays
        self.show_invalid_claim_display = False
        self.show_missed_winner_display = False
        self.show_winner_display = False
        self.show_winner_validation = False
        self.show_pause_reason_prompt = False

        # Reset the audio flags in the UI handler
        # This ensures the sounds will play again for the next claim window
        if hasattr(self.game, 'ui_handler'):
            if hasattr(self.game.ui_handler, '_played_invalid_claim_sound'):
                delattr(self.game.ui_handler, '_played_invalid_claim_sound')
                print("Reset _played_invalid_claim_sound flag when penalizing player")

            if hasattr(self.game.ui_handler, '_played_missed_winner_sound'):
                delattr(self.game.ui_handler, '_played_missed_winner_sound')
                print("Reset _played_missed_winner_sound flag when penalizing player")

        # CRITICAL FIX: Reset all claim-related flags
        self.checking_next_claim = False
        self.late_claim = False
        self.next_number_called = False
        self.was_next_number_called = False
        self.check_with_previous_numbers = False

        # CRITICAL FIX: Reset the pause state
        self.is_paused = False
        self.pause_reason = None
        self.pause_reason_number = None
        self.player_claim_cartella = None

        print("CRITICAL FIX: Reset all game state flags after penalization")

        # Resume the game immediately without audio delay
        # For penalization, we want immediate resumption, not delayed audio resumption
        print("CRITICAL FIX: Calling resume_game directly after penalization for immediate resumption")
        self.resume_game()

        return True

    def check_next_claim(self):
        """
        Close the current claim window and return to the pause reason prompt
        to check another claim

        Returns:
            bool: True if successful
        """
        # Close the current claim window
        if self.show_winner_display:
            self.show_winner_display = False
        if self.show_invalid_claim_display:
            self.show_invalid_claim_display = False
        if self.show_missed_winner_display:
            self.show_missed_winner_display = False

        # Clear winner animation particles if they exist
        if hasattr(self.game, 'ui_handler') and hasattr(self.game.ui_handler, 'winner_particles'):
            self.game.ui_handler.winner_particles = []
            delattr(self.game.ui_handler, 'winner_particles')
            print("Cleared winner animation particles when checking next claim")

        # Reset the audio flags in the UI handler
        # This ensures the sounds will play again for the next claim window
        if hasattr(self.game, 'ui_handler'):
            if hasattr(self.game.ui_handler, '_played_invalid_claim_sound'):
                delattr(self.game.ui_handler, '_played_invalid_claim_sound')
                print("Reset _played_invalid_claim_sound flag when checking next claim")

            if hasattr(self.game.ui_handler, '_played_missed_winner_sound'):
                delattr(self.game.ui_handler, '_played_missed_winner_sound')
                print("Reset _played_missed_winner_sound flag when checking next claim")

        # Reset claim-related fields
        self.validation_result = None
        self.invalid_claim_reason = None
        self.winner_pattern = None
        self.player_claim_cartella = None
        self.claim_type = None
        self.winning_number = None
        self.last_winning_chance_number = None

        # CRITICAL FIX: Set a flag to indicate we're checking multiple claims
        # This will be used to prevent false "late claim" detections
        self.checking_next_claim = True

        # Reset the next_number_called and was_next_number_called flags when checking another claim
        # This ensures that the next claim is not incorrectly marked as late
        self.next_number_called = False
        self.was_next_number_called = False

        # Reset the late_claim flag
        self.late_claim = False

        # Reset the check_with_previous_numbers flag
        self.check_with_previous_numbers = False

        # CRITICAL FIX: Don't reset previous_number tracking
        # We want to keep track of the previous number for accurate late claim detection
        # But we do log it for debugging purposes
        if hasattr(self, 'previous_number') and self.previous_number is not None:
            print(f"Keeping previous_number = {self.previous_number} for accurate late claim detection")

        # Reset the last_number_time to current time
        # This ensures that the time-based check doesn't incorrectly mark claims as late
        self.last_number_time = pygame.time.get_ticks()
        print(f"Reset next_number_called, was_next_number_called flags and last_number_time for check_next_claim")

        # Show the pause reason prompt again
        self.show_pause_reason_prompt = True

        return True

    def close_admin_control(self):
        """Close the admin control window - now just a wrapper for resume_game for backward compatibility"""
        print("CLOSE_ADMIN_CONTROL called - This should safely close the admin control window")

        # CRITICAL FIX: Verify that the game is actually started before resuming
        # This prevents accidentally resetting the game when it's not actually running
        if not hasattr(self.game, 'game_started') or not self.game.game_started:
            print("CRITICAL ERROR: Attempted to resume a game that hasn't been started!")
            print("This could cause the game to reset instead of resume.")
            # Just close the window without resuming
            self.show_admin_control = False
            return

        # CRITICAL FIX: Verify that the game is actually paused before resuming
        if not self.is_paused:
            print("CRITICAL ERROR: Attempted to resume a game that isn't paused!")
            print("This could cause the game to reset instead of resume.")
            # Just close the window without resuming
            self.show_admin_control = False
            return

        self.show_admin_control = False

        # CRITICAL FIX: Add additional safety check before resuming
        try:
            # By default, return to main page
            # We don't need to check the return value here since this is just closing the window
            # without confirming a reset
            # Use the new resume method with audio if available
            if hasattr(self.game, 'resume_game_with_audio'):
                self.game.resume_game_with_audio()
            else:
                # Fallback to old method
                self.resume_game()
        except Exception as e:
            print(f"ERROR resuming game: {e}")
            print("This might indicate a deeper issue with the game state.")
            # Don't let exceptions propagate - we want to keep the game running

    def get_winning_pattern_numbers(self, card, pattern_name):
        """
        Get the numbers in a specific winning pattern on a card

        Args:
            card: The bingo card object
            pattern_name: The name of the winning pattern

        Returns:
            list: List of numbers in the winning pattern
        """
        if not pattern_name:
            return []

        # CRITICAL FIX: Use pattern_utils to normalize the pattern name
        try:
            import pattern_utils
            normalized_name = pattern_utils.normalize_pattern_name(pattern_name)
            print(f"CRITICAL FIX: Normalized pattern name '{pattern_name}' to '{normalized_name}'")

            # If the pattern name was normalized, try to get the pattern cells directly from pattern_utils
            if normalized_name != pattern_name:
                pattern_cells = pattern_utils.get_pattern_cells_by_name(normalized_name)
                if pattern_cells:
                    # Convert pattern cells to numbers using the card's grid
                    numbers = []
                    if hasattr(card, 'grid'):
                        for row, col in pattern_cells:
                            num = card.grid[col][row]  # Note: grid is column-major, so we use [col][row]
                            if num != 0:  # Skip free space
                                numbers.append(num)
                        print(f"CRITICAL FIX: Got numbers for pattern '{normalized_name}' using pattern_utils: {numbers}")
                        return numbers
        except Exception as e:
            print(f"CRITICAL FIX: Error using pattern_utils: {e}")
            # Continue with the original implementation as fallback

        # Check if we're using the grid or numbers attribute
        if hasattr(card, 'grid'):
            grid_attr = 'grid'
        elif hasattr(card, 'numbers'):
            grid_attr = 'numbers'
        else:
            return []

        grid = getattr(card, grid_attr)
        numbers = []

        # Handle different pattern types
        if "Row" in pattern_name:
            # Extract row number (1-5)
            try:
                row_num = int(pattern_name.split()[-1]) - 1  # Convert to 0-based index
                if 0 <= row_num < 5:
                    # Get all numbers in this row
                    for col in range(5):
                        # Skip the FREE space in the middle
                        if row_num == 2 and col == 2:
                            continue
                        num = grid[col][row_num] if grid_attr == 'grid' else grid[row_num][col]
                        if num != 0:  # Skip free space
                            numbers.append(num)
            except (ValueError, IndexError):
                pass

        elif "Column" in pattern_name:
            # Extract column number (1-5)
            try:
                col_num = int(pattern_name.split()[-1]) - 1  # Convert to 0-based index
                if 0 <= col_num < 5:
                    # Get all numbers in this column
                    for row in range(5):
                        # Skip the FREE space in the middle
                        if row == 2 and col_num == 2:
                            continue
                        num = grid[col_num][row] if grid_attr == 'grid' else grid[row][col_num]
                        if num != 0:  # Skip free space
                            numbers.append(num)
            except (ValueError, IndexError):
                pass

        elif "Diagonal" in pattern_name:
            # CRITICAL FIX: Improved diagonal pattern detection with better pattern name matching
            # This handles both full pattern names and simplified versions

            # Log the pattern name for debugging
            print(f"CRITICAL FIX: Processing diagonal pattern: {pattern_name}")

            # Check for main diagonal (top-left to bottom-right)
            if "top-left" in pattern_name.lower() or (
                "diagonal" in pattern_name.lower() and
                not "top-right" in pattern_name.lower() and
                not "bottom-left" in pattern_name.lower()
            ):
                print(f"CRITICAL FIX: Detected main diagonal (top-left to bottom-right)")
                # Main diagonal (top-left to bottom-right)
                for i in range(5):
                    # Skip the FREE space in the middle
                    if i == 2:
                        continue
                    num = grid[i][i] if grid_attr == 'grid' else grid[i][i]
                    if num != 0:  # Skip free space
                        numbers.append(num)
                print(f"CRITICAL FIX: Main diagonal numbers: {numbers}")

            # Check for anti-diagonal (top-right to bottom-left)
            elif "top-right" in pattern_name.lower() or "bottom-left" in pattern_name.lower():
                print(f"CRITICAL FIX: Detected anti-diagonal (top-right to bottom-left)")
                # Anti-diagonal (top-right to bottom-left)
                for i in range(5):
                    # Skip the FREE space in the middle
                    if i == 2:
                        continue
                    num = grid[i][4-i] if grid_attr == 'grid' else grid[i][4-i]
                    if num != 0:  # Skip free space
                        numbers.append(num)
                print(f"CRITICAL FIX: Anti-diagonal numbers: {numbers}")

            # If we can't determine which diagonal, try both
            else:
                print(f"CRITICAL FIX: Could not determine which diagonal, trying both")

                # Try main diagonal first
                main_diagonal = []
                for i in range(5):
                    # Skip the FREE space in the middle
                    if i == 2:
                        continue
                    num = grid[i][i] if grid_attr == 'grid' else grid[i][i]
                    if num != 0:  # Skip free space
                        main_diagonal.append(num)

                # Try anti-diagonal
                anti_diagonal = []
                for i in range(5):
                    # Skip the FREE space in the middle
                    if i == 2:
                        continue
                    num = grid[i][4-i] if grid_attr == 'grid' else grid[i][4-i]
                    if num != 0:  # Skip free space
                        anti_diagonal.append(num)

                # Use the one that has more numbers (more likely to be the correct one)
                if len(main_diagonal) >= len(anti_diagonal):
                    numbers = main_diagonal
                    print(f"CRITICAL FIX: Using main diagonal: {numbers}")
                else:
                    numbers = anti_diagonal
                    print(f"CRITICAL FIX: Using anti-diagonal: {numbers}")

        elif "Four Corners" in pattern_name:
            # Four corners pattern
            corners = [(0, 0), (0, 4), (4, 0), (4, 4)]
            for col, row in corners:  # Note: grid is column-major, so we use [col][row]
                num = grid[col][row] if grid_attr == 'grid' else grid[row][col]
                if num != 0:  # Skip empty cells
                    numbers.append(num)

        elif "Blackout" in pattern_name or "Coverall" in pattern_name:
            # All numbers on the card except the FREE space
            for col in range(5):
                for row in range(5):
                    # Skip the FREE space in the middle
                    if row == 2 and col == 2:
                        continue
                    num = grid[col][row] if grid_attr == 'grid' else grid[row][col]
                    if num != 0:  # Skip empty cells
                        numbers.append(num)

        return numbers

    def is_cartella_registered(self, cartella_number):
        """Check if the cartella number is registered in the game"""
        # Look through the players list to find the cartella
        for player in self.game.players:
            if player.cartela_no == cartella_number:
                return True
        return False

    def close_validation_display(self):
        """Close the validation display and handle next steps"""
        print("CLOSE_VALIDATION_DISPLAY called - This should safely close the validation window")

        # CRITICAL FIX: Verify that the game is actually started before resuming
        # This prevents accidentally resetting the game when it's not actually running
        if not hasattr(self.game, 'game_started') or not self.game.game_started:
            print("CRITICAL ERROR: Attempted to resume a game that hasn't been started!")
            print("This could cause the game to reset instead of resume.")
            # Just close the windows without resuming
            self.show_winner_display = False
            self.show_invalid_claim_display = False
            self.show_missed_winner_display = False
            return

        # CRITICAL FIX: Verify that the game is actually paused before resuming
        if not self.is_paused:
            print("CRITICAL ERROR: Attempted to resume a game that isn't paused!")
            print("This could cause the game to reset instead of resume.")
            # Just close the windows without resuming
            self.show_winner_display = False
            self.show_invalid_claim_display = False
            self.show_missed_winner_display = False
            return

        if self.show_winner_display:
            # Just close the winner display and resume the game
            # This allows checking multiple winners without resetting the game
            self.show_winner_display = False

            # Clear winner animation particles if they exist
            if hasattr(self.game, 'ui_handler') and hasattr(self.game.ui_handler, 'winner_particles'):
                self.game.ui_handler.winner_particles = []
                delattr(self.game.ui_handler, 'winner_particles')
                print("Cleared winner animation particles")

            # CRITICAL FIX: Add additional safety check before resuming
            try:
                # Resume the game so players can continue playing
                # Use the new resume method with audio if available
                if hasattr(self.game, 'resume_game_with_audio'):
                    self.game.resume_game_with_audio()
                else:
                    # Fallback to old method
                    self.resume_game()
            except Exception as e:
                print(f"ERROR resuming game: {e}")
                print("This might indicate a deeper issue with the game state.")
                # Don't let exceptions propagate - we want to keep the game running
            return

        if self.show_invalid_claim_display:
            # If it was an invalid claim, resume the game
            self.show_invalid_claim_display = False

            # Reset the _played_invalid_claim_sound flag in the UI handler
            # This ensures the sound will play again the next time the window is shown
            if hasattr(self.game, 'ui_handler') and hasattr(self.game.ui_handler, '_played_invalid_claim_sound'):
                delattr(self.game.ui_handler, '_played_invalid_claim_sound')
                print("Reset _played_invalid_claim_sound flag")

            # CRITICAL FIX: Add additional safety check before resuming
            try:
                # Use the new resume method with audio if available
                if hasattr(self.game, 'resume_game_with_audio'):
                    self.game.resume_game_with_audio()
                else:
                    # Fallback to old method
                    self.resume_game()
            except Exception as e:
                print(f"ERROR resuming game: {e}")
                print("This might indicate a deeper issue with the game state.")
                # Don't let exceptions propagate - we want to keep the game running
            return

        if self.show_missed_winner_display:
            # If it was a missed winner, resume the game
            self.show_missed_winner_display = False

            # Clear winner animation particles if they exist (for valid winners)
            if hasattr(self.game, 'ui_handler') and hasattr(self.game.ui_handler, 'winner_particles'):
                self.game.ui_handler.winner_particles = []
                delattr(self.game.ui_handler, 'winner_particles')
                print("Cleared winner animation particles from missed winner display")

            # Reset the _played_missed_winner_sound flag in the UI handler
            # This ensures the sound will play again the next time the window is shown
            if hasattr(self.game, 'ui_handler') and hasattr(self.game.ui_handler, '_played_missed_winner_sound'):
                delattr(self.game.ui_handler, '_played_missed_winner_sound')
                print("Reset _played_missed_winner_sound flag")

            # CRITICAL FIX: Add additional safety check before resuming
            try:
                # Use the new resume method with audio if available
                if hasattr(self.game, 'resume_game_with_audio'):
                    self.game.resume_game_with_audio()
                else:
                    # Fallback to old method
                    self.resume_game()
            except Exception as e:
                print(f"ERROR resuming game: {e}")
                print("This might indicate a deeper issue with the game state.")
                # Don't let exceptions propagate - we want to keep the game running
            return

    def penalise_player(self, cartella_number):
        """
        Penalise a player by removing them from the current game session

        Args:
            cartella_number: The cartella number to penalise

        Returns:
            bool: True if successful, False otherwise
        """
        # Check if the cartella is registered
        if not self.is_cartella_registered(cartella_number):
            return False

        # Find the player in the players list
        player_index = None
        for i, player in enumerate(self.game.players):
            if player.cartela_no == cartella_number:
                player_index = i
                break

        if player_index is None:
            return False

        # Audio is now played directly in the UI handler when the button is clicked
        # No need to play audio here

        # Remove the player from the current game session
        self.game.players.pop(player_index)

        # Log the penalisation
        print(f"Player with cartella {cartella_number} penalised and removed from game session")

        # Show a message to the user
        if hasattr(self.game, 'message'):
            self.game.message = f"Player with cartella {cartella_number} penalised"
            self.game.message_type = "warning"
            self.game.message_timer = 180  # 3 seconds at 60 FPS

        # CRITICAL FIX: Close all claim displays
        self.show_invalid_claim_display = False
        self.show_missed_winner_display = False
        self.show_winner_display = False
        self.show_winner_validation = False
        self.show_pause_reason_prompt = False

        # Reset the audio flags in the UI handler
        # This ensures the sounds will play again for the next claim window
        if hasattr(self.game, 'ui_handler'):
            if hasattr(self.game.ui_handler, '_played_invalid_claim_sound'):
                delattr(self.game.ui_handler, '_played_invalid_claim_sound')
                print("Reset _played_invalid_claim_sound flag when penalizing player")

            if hasattr(self.game.ui_handler, '_played_missed_winner_sound'):
                delattr(self.game.ui_handler, '_played_missed_winner_sound')
                print("Reset _played_missed_winner_sound flag when penalizing player")

        # CRITICAL FIX: Reset all claim-related flags
        self.checking_next_claim = False
        self.late_claim = False
        self.next_number_called = False
        self.was_next_number_called = False
        self.check_with_previous_numbers = False

        # CRITICAL FIX: Reset the pause state
        self.is_paused = False
        self.pause_reason = None
        self.pause_reason_number = None
        self.player_claim_cartella = None

        print("CRITICAL FIX: Reset all game state flags after penalization")

        # Resume the game immediately without audio delay
        # For penalization, we want immediate resumption, not delayed audio resumption
        print("CRITICAL FIX: Calling resume_game directly after penalization for immediate resumption")
        self.resume_game()

        return True

    def check_next_claim(self):
        """
        Close the current claim window and return to the pause reason prompt
        to check another claim

        Returns:
            bool: True if successful
        """
        # Close the current claim window
        if self.show_winner_display:
            self.show_winner_display = False
        if self.show_invalid_claim_display:
            self.show_invalid_claim_display = False
        if self.show_missed_winner_display:
            self.show_missed_winner_display = False

        # Clear winner animation particles if they exist
        if hasattr(self.game, 'ui_handler') and hasattr(self.game.ui_handler, 'winner_particles'):
            self.game.ui_handler.winner_particles = []
            delattr(self.game.ui_handler, 'winner_particles')
            print("Cleared winner animation particles when checking next claim")

        # Reset the audio flags in the UI handler
        # This ensures the sounds will play again for the next claim window
        if hasattr(self.game, 'ui_handler'):
            if hasattr(self.game.ui_handler, '_played_invalid_claim_sound'):
                delattr(self.game.ui_handler, '_played_invalid_claim_sound')
                print("Reset _played_invalid_claim_sound flag when checking next claim")

            if hasattr(self.game.ui_handler, '_played_missed_winner_sound'):
                delattr(self.game.ui_handler, '_played_missed_winner_sound')
                print("Reset _played_missed_winner_sound flag when checking next claim")

        # Reset claim-related fields
        self.validation_result = None
        self.invalid_claim_reason = None
        self.winner_pattern = None
        self.player_claim_cartella = None
        self.claim_type = None
        self.winning_number = None
        self.last_winning_chance_number = None

        # CRITICAL FIX: Set a flag to indicate we're checking multiple claims
        # This will be used to prevent false "late claim" detections
        self.checking_next_claim = True

        # Reset the next_number_called and was_next_number_called flags when checking another claim
        # This ensures that the next claim is not incorrectly marked as late
        self.next_number_called = False
        self.was_next_number_called = False

        # Reset the late_claim flag
        self.late_claim = False

        # Reset the check_with_previous_numbers flag
        self.check_with_previous_numbers = False

        # CRITICAL FIX: Don't reset previous_number tracking
        # We want to keep track of the previous number for accurate late claim detection
        # But we do log it for debugging purposes
        if hasattr(self, 'previous_number') and self.previous_number is not None:
            print(f"Keeping previous_number = {self.previous_number} for accurate late claim detection")

        # Reset the last_number_time to current time
        # This ensures that the time-based check doesn't incorrectly mark claims as late
        self.last_number_time = pygame.time.get_ticks()
        print(f"Reset next_number_called, was_next_number_called flags and last_number_time for check_next_claim")

        # Show the pause reason prompt again
        self.show_pause_reason_prompt = True

        return True

    def close_admin_control(self):
        """Close the admin control window - now just a wrapper for resume_game for backward compatibility"""
        print("CLOSE_ADMIN_CONTROL called - This should safely close the admin control window")

        # CRITICAL FIX: Verify that the game is actually started before resuming
        # This prevents accidentally resetting the game when it's not actually running
        if not hasattr(self.game, 'game_started') or not self.game.game_started:
            print("CRITICAL ERROR: Attempted to resume a game that hasn't been started!")
            print("This could cause the game to reset instead of resume.")
            # Just close the window without resuming
            self.show_admin_control = False
            return

        # CRITICAL FIX: Verify that the game is actually paused before resuming
        if not self.is_paused:
            print("CRITICAL ERROR: Attempted to resume a game that isn't paused!")
            print("This could cause the game to reset instead of resume.")
            # Just close the window without resuming
            self.show_admin_control = False
            return

        self.show_admin_control = False

        # CRITICAL FIX: Add additional safety check before resuming
        try:
            # By default, return to main page
            # We don't need to check the return value here since this is just closing the window
            # without confirming a reset
            # Use the new resume method with audio if available
            if hasattr(self.game, 'resume_game_with_audio'):
                self.game.resume_game_with_audio()
            else:
                # Fallback to old method
                self.resume_game()
        except Exception as e:
            print(f"ERROR resuming game: {e}")
            print("This might indicate a deeper issue with the game state.")
            # Don't let exceptions propagate - we want to keep the game running

def get_current_game_state():
    """
    Get the current game state information.

    This function provides access to the current game state for external modules
    like the RethinkDB integration system.

    Returns:
        dict: Dictionary containing current game state information
    """
    try:
        # Try to find the current game instance
        import gc

        # Look for BingoGame instances in memory
        for obj in gc.get_objects():
            if hasattr(obj, '__class__') and hasattr(obj.__class__, '__name__'):
                if obj.__class__.__name__ == 'BingoGame':
                    # Found a BingoGame instance
                    game_state = {
                        'game_started': getattr(obj, 'game_started', False),
                        'is_paused': False,
                        'current_number': getattr(obj, 'current_number', None),
                        'called_numbers': getattr(obj, 'called_numbers', []),
                        'player_count': len(getattr(obj, 'players', [])),
                        'is_demo_mode': getattr(obj, 'is_demo_mode', False),
                        'bet_amount': getattr(obj, 'bet_amount', 50),
                        'prize_pool': getattr(obj, 'prize_pool', 0),
                        'commission_percentage': getattr(obj, 'commission_percentage', 20)
                    }

                    # Check if the game has a game state handler
                    if hasattr(obj, 'game_state_handler'):
                        handler = obj.game_state_handler
                        game_state['is_paused'] = getattr(handler, 'is_paused', False)
                        game_state['pause_reason'] = getattr(handler, 'pause_reason', None)
                        game_state['show_winner_display'] = getattr(handler, 'show_winner_display', False)
                        game_state['winner_pattern'] = getattr(handler, 'winner_pattern', None)

                    return game_state

        # If no game instance found, return default state
        return {
            'game_started': False,
            'is_paused': False,
            'current_number': None,
            'called_numbers': [],
            'player_count': 0,
            'is_demo_mode': False,
            'bet_amount': 50,
            'prize_pool': 0,
            'commission_percentage': 20,
            'pause_reason': None,
            'show_winner_display': False,
            'winner_pattern': None
        }

    except Exception as e:
        print(f"Error getting current game state: {e}")
        # Return a safe default state
        return {
            'game_started': False,
            'is_paused': False,
            'current_number': None,
            'called_numbers': [],
            'player_count': 0,
            'is_demo_mode': False,
            'bet_amount': 50,
            'prize_pool': 0,
            'commission_percentage': 20,
            'pause_reason': None,
            'show_winner_display': False,
            'winner_pattern': None,
            'error': str(e)
        }

